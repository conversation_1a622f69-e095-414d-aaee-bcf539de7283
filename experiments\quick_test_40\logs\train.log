2025-06-10 16:21:55,954 - INFO - Arguments:
2025-06-10 16:21:55,955 - INFO -   data_root: ./data
2025-06-10 16:21:55,955 - INFO -   n_labeled: 40
2025-06-10 16:21:55,955 - INFO -   batch_size: 16
2025-06-10 16:21:55,955 - INFO -   mu: 7
2025-06-10 16:21:55,955 - INFO -   epochs: 5
2025-06-10 16:21:55,955 - INFO -   lr: 0.03
2025-06-10 16:21:55,955 - INFO -   weight_decay: 0.0005
2025-06-10 16:21:55,955 - INFO -   momentum: 0.9
2025-06-10 16:21:55,955 - INFO -   threshold: 0.95
2025-06-10 16:21:55,955 - INFO -   lambda_u: 1.0
2025-06-10 16:21:55,955 - INFO -   use_ema: True
2025-06-10 16:21:55,955 - INFO -   ema_decay: 0.999
2025-06-10 16:21:55,955 - INFO -   use_amp: False
2025-06-10 16:21:55,955 - INFO -   num_workers: 0
2025-06-10 16:21:55,957 - INFO -   seed: 42
2025-06-10 16:21:55,957 - INFO -   log_dir: experiments/quick_test_40/logs
2025-06-10 16:21:55,957 - INFO -   checkpoint_dir: experiments/quick_test_40/checkpoints
2025-06-10 16:21:55,958 - INFO -   log_interval: 100
2025-06-10 16:21:55,958 - INFO -   eval_interval: 2
2025-06-10 16:21:55,958 - INFO -   save_interval: 100
2025-06-10 16:21:55,958 - INFO - Using device: cuda
2025-06-10 16:21:55,967 - INFO - Creating data loaders with 40 labeled samples per class
2025-06-10 16:21:59,342 - INFO - Labeled samples: 400
2025-06-10 16:21:59,342 - INFO - Unlabeled samples: 49600
2025-06-10 16:21:59,342 - INFO - Test samples: 10000
2025-06-10 16:21:59,460 - INFO - Starting training...
2025-06-10 16:23:04,551 - INFO - Arguments:
2025-06-10 16:23:04,551 - INFO -   data_root: ./data
2025-06-10 16:23:04,551 - INFO -   n_labeled: 40
2025-06-10 16:23:04,551 - INFO -   batch_size: 16
2025-06-10 16:23:04,551 - INFO -   mu: 7
2025-06-10 16:23:04,551 - INFO -   epochs: 5
2025-06-10 16:23:04,551 - INFO -   lr: 0.03
2025-06-10 16:23:04,551 - INFO -   weight_decay: 0.0005
2025-06-10 16:23:04,551 - INFO -   momentum: 0.9
2025-06-10 16:23:04,551 - INFO -   threshold: 0.95
2025-06-10 16:23:04,551 - INFO -   lambda_u: 1.0
2025-06-10 16:23:04,551 - INFO -   use_ema: True
2025-06-10 16:23:04,551 - INFO -   ema_decay: 0.999
2025-06-10 16:23:04,551 - INFO -   use_amp: False
2025-06-10 16:23:04,551 - INFO -   num_workers: 0
2025-06-10 16:23:04,551 - INFO -   seed: 42
2025-06-10 16:23:04,552 - INFO -   log_dir: experiments/quick_test_40/logs
2025-06-10 16:23:04,552 - INFO -   checkpoint_dir: experiments/quick_test_40/checkpoints
2025-06-10 16:23:04,552 - INFO -   log_interval: 100
2025-06-10 16:23:04,552 - INFO -   eval_interval: 2
2025-06-10 16:23:04,552 - INFO -   save_interval: 100
2025-06-10 16:23:04,552 - INFO - Using device: cuda
2025-06-10 16:23:04,554 - INFO - Creating data loaders with 40 labeled samples per class
2025-06-10 16:23:07,666 - INFO - Labeled samples: 400
2025-06-10 16:23:07,667 - INFO - Unlabeled samples: 49600
2025-06-10 16:23:07,667 - INFO - Test samples: 10000
2025-06-10 16:23:07,761 - INFO - Starting training...
2025-06-10 16:24:02,994 - INFO - Arguments:
2025-06-10 16:24:02,994 - INFO -   data_root: ./data
2025-06-10 16:24:02,994 - INFO -   n_labeled: 40
2025-06-10 16:24:02,994 - INFO -   batch_size: 16
2025-06-10 16:24:02,994 - INFO -   mu: 7
2025-06-10 16:24:02,994 - INFO -   epochs: 5
2025-06-10 16:24:02,995 - INFO -   lr: 0.03
2025-06-10 16:24:02,995 - INFO -   weight_decay: 0.0005
2025-06-10 16:24:02,995 - INFO -   momentum: 0.9
2025-06-10 16:24:02,995 - INFO -   threshold: 0.95
2025-06-10 16:24:02,995 - INFO -   lambda_u: 1.0
2025-06-10 16:24:02,995 - INFO -   use_ema: True
2025-06-10 16:24:02,995 - INFO -   ema_decay: 0.999
2025-06-10 16:24:02,995 - INFO -   use_amp: False
2025-06-10 16:24:02,995 - INFO -   num_workers: 0
2025-06-10 16:24:02,995 - INFO -   seed: 42
2025-06-10 16:24:02,995 - INFO -   log_dir: experiments/quick_test_40/logs
2025-06-10 16:24:02,995 - INFO -   checkpoint_dir: experiments/quick_test_40/checkpoints
2025-06-10 16:24:02,995 - INFO -   log_interval: 100
2025-06-10 16:24:02,995 - INFO -   eval_interval: 2
2025-06-10 16:24:02,995 - INFO -   save_interval: 100
2025-06-10 16:24:02,995 - INFO - Using device: cuda
2025-06-10 16:24:02,997 - INFO - Creating data loaders with 40 labeled samples per class
2025-06-10 16:24:06,161 - INFO - Labeled samples: 400
2025-06-10 16:24:06,161 - INFO - Unlabeled samples: 49600
2025-06-10 16:24:06,161 - INFO - Test samples: 10000
2025-06-10 16:24:06,253 - INFO - Starting training...
2025-06-10 16:24:14,887 - INFO - Epoch 1/5 - Time: 8.63s
2025-06-10 16:24:14,888 - INFO - Loss: 2.3560, L_sup: 2.3553, L_unsup: 0.0007
2025-06-10 16:24:14,888 - INFO - Labeled Acc: 0.0975, Mask Prob: 0.0014
2025-06-10 16:24:23,328 - INFO - Epoch 2/5 - Time: 8.44s
2025-06-10 16:24:23,328 - INFO - Loss: 2.2869, L_sup: 2.2764, L_unsup: 0.0105
2025-06-10 16:24:23,328 - INFO - Labeled Acc: 0.1825, Mask Prob: 0.0050
2025-06-10 16:24:30,406 - INFO - Test Loss: 2.6679, Test Acc: 0.1000
2025-06-10 16:24:30,458 - INFO - Checkpoint saved to experiments/quick_test_40/checkpoints\best_model.pth
2025-06-10 16:24:39,103 - INFO - Epoch 3/5 - Time: 8.64s
2025-06-10 16:24:39,103 - INFO - Loss: 2.0754, L_sup: 2.0683, L_unsup: 0.0070
2025-06-10 16:24:39,103 - INFO - Labeled Acc: 0.2400, Mask Prob: 0.0054
2025-06-10 16:24:47,719 - INFO - Epoch 4/5 - Time: 8.62s
2025-06-10 16:24:47,719 - INFO - Loss: 1.9892, L_sup: 1.9847, L_unsup: 0.0046
2025-06-10 16:24:47,719 - INFO - Labeled Acc: 0.2550, Mask Prob: 0.0046
2025-06-10 16:24:54,347 - INFO - Test Loss: 2.4598, Test Acc: 0.1000
2025-06-10 16:25:02,536 - INFO - Epoch 5/5 - Time: 8.19s
2025-06-10 16:25:02,536 - INFO - Loss: 1.8166, L_sup: 1.8165, L_unsup: 0.0002
2025-06-10 16:25:02,536 - INFO - Labeled Acc: 0.3125, Mask Prob: 0.0007
2025-06-10 16:25:02,536 - INFO - Training completed! Best test accuracy: 0.1000
