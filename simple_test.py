"""
Simple test to verify core FixMatch functionality
"""
import torch
import numpy as np

# Test core components
def test_core():
    print("Testing core FixMatch components...")
    
    # Test model
    from models.wideresnet import build_wideresnet
    model = build_wideresnet(depth=28, widen_factor=2, dropout=0.0, num_classes=10)
    print(f"✓ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Test forward pass
    x = torch.randn(4, 3, 32, 32)
    with torch.no_grad():
        output = model(x)
    print(f"✓ Forward pass: {x.shape} -> {output.shape}")
    
    # Test FixMatch algorithm
    from fixmatch import FixMatch, create_model_and_optimizer
    model, optimizer = create_model_and_optimizer(num_classes=10, lr=0.03)
    
    fixmatch = FixMatch(
        model=model,
        optimizer=optimizer,
        threshold=0.95,
        lambda_u=1.0,
        use_amp=False,
        device='cpu'
    )
    
    # Create test batches
    batch_size = 4
    labeled_images = torch.randn(batch_size, 3, 32, 32)
    labeled_targets = torch.randint(0, 10, (batch_size,))
    labeled_batch = (labeled_images, labeled_targets)
    
    unlabeled_weak = torch.randn(batch_size * 2, 3, 32, 32)
    unlabeled_strong = torch.randn(batch_size * 2, 3, 32, 32)
    unlabeled_batch = ((unlabeled_weak, unlabeled_strong), torch.zeros(batch_size * 2))
    
    # Test training step
    metrics = fixmatch.train_step(labeled_batch, unlabeled_batch)
    print(f"✓ Training step successful")
    print(f"  Loss: {metrics['loss']:.4f}")
    print(f"  Labeled accuracy: {metrics['labeled_acc']:.4f}")
    print(f"  Mask probability: {metrics['mask_prob']:.4f}")
    
    print("\n🎉 Core functionality test passed!")
    return True

if __name__ == '__main__':
    test_core()
