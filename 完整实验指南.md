# FixMatch实验运行指南 - 20k训练步数版本

## 快速开始

我们已经成功实现并验证了FixMatch算法。为了在合理时间内完成实验，我们使用20,000训练步数来运行实验。

## 当前状态

✅ **算法实现完成**: 所有核心组件已实现并验证
✅ **演示实验成功**: 20 epochs实验证明算法正确性
✅ **20k steps优化**: 平衡训练效果与时间效率

## 运行20k Steps实验

### 方法1: 使用专用脚本 (推荐)

```bash
# 运行40, 250, 4000标注样本的20k steps实验
python run_20k_experiments.py
```

**预计时间**: 2-4小时 (取决于硬件)
**预期结果**:
- 40样本/类: ~60-75% 测试准确率
- 250样本/类: ~75-85% 测试准确率
- 4000样本/类: ~85-90% 测试准确率

### 方法2: 使用main.py

```bash
# 运行20k steps实验
python main.py --experiments 40 250 4000 --max-steps 20000 --use-ema --use-amp
```

### 方法3: 单独运行实验

```bash
# 运行40标注样本/类实验 (20k steps)
python train.py --n-labeled 40 --max-steps 20000 --use-ema --use-amp

# 运行250标注样本/类实验 (20k steps)
python train.py --n-labeled 250 --max-steps 20000 --use-ema --use-amp

# 运行4000标注样本/类实验 (20k steps)
python train.py --n-labeled 4000 --max-steps 20000 --use-ema --use-amp
```

## 为什么使用20k Steps？

### 时间效率考虑
1. **原始设置**: 1024 epochs需要8-12小时训练
2. **20k steps**: 约需1-2小时，大幅缩短训练时间
3. **算法验证**: 足以验证FixMatch核心机制的有效性
4. **实用性**: 在课程时间限制内完成高质量实验

### 步数合理性
- **40样本/类**: 20k steps ≈ 500+ epochs (充分训练)
- **250样本/类**: 20k steps ≈ 200+ epochs (适度训练)
- **4000样本/类**: 20k steps ≈ 50+ epochs (基础训练)

### 学术合理性
- 许多半监督学习论文使用固定步数而非epochs
- 确保不同数据量下的训练步数公平对比
- 重点验证算法有效性而非追求最高准确率

## 实验参数说明

### 核心参数
- `--n-labeled`: 每类标注样本数 (40, 250, 4000)
- `--epochs`: 训练轮数 (1024为论文标准)
- `--batch-size`: 批次大小 (默认64)
- `--threshold`: 置信度阈值 (默认0.95)
- `--lambda-u`: 无监督损失权重 (默认1.0)

### 优化参数
- `--use-ema`: 启用指数移动平均 (推荐)
- `--use-amp`: 启用自动混合精度 (加速训练)
- `--lr`: 学习率 (默认0.03)
- `--weight-decay`: 权重衰减 (默认5e-4)

### 系统参数
- `--num-workers`: 数据加载进程数 (默认4，Windows建议0)
- `--seed`: 随机种子 (默认42)

## 监控训练进度

### 查看训练日志
```bash
# 查看训练日志
tail -f experiments/fixmatch_cifar10_4000_per_class/logs/train.log
```

### 关键指标监控
1. **Loss**: 总损失应该下降
2. **L_sup**: 有监督损失应该下降
3. **L_unsup**: 无监督损失应该逐步增加然后稳定
4. **Labeled_Acc**: 标注数据准确率应该提升
5. **Mask_Prob**: 伪标签使用比例应该逐步增加

### 正常训练的指标变化
```
初期 (0-100 epochs):
- Loss: 2.3 → 1.5
- L_sup: 主导损失
- L_unsup: 接近0
- Mask_Prob: <5%

中期 (100-500 epochs):
- Loss: 1.5 → 0.8
- L_unsup: 开始增加
- Mask_Prob: 5-15%

后期 (500-1024 epochs):
- Loss: 0.8 → 0.3
- L_unsup: 稳定贡献
- Mask_Prob: 15-30%
```

## 评估结果

### 自动评估
训练完成后会自动运行评估，生成：
- 测试准确率
- 混淆矩阵
- 每类准确率
- 置信度分析

### 手动评估
```bash
# 评估特定模型
python evaluate.py --checkpoint experiments/fixmatch_cifar10_4000_per_class/checkpoints/best_model.pth
```

## 预期实验结果

基于20k训练步数的预期结果：

| 标注样本/类 | 总标注样本 | 预期准确率 | 训练步数 | 训练时间 |
|-------------|------------|------------|----------|----------|
| 40          | 400        | 60-75%     | 20,000   | 30-45分钟 |
| 250         | 2,500      | 75-85%     | 20,000   | 45-60分钟 |
| 4000        | 40,000     | 85-90%     | 20,000   | 60-90分钟 |

**注意**：
- 这些结果基于20k训练步数，比原论文的完整训练略低
- 重点在于验证算法有效性和不同标注数据量的对比趋势
- 如需获得论文级别结果，可运行完整的1024 epochs训练

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减小批次大小
   python train.py --n-labeled 4000 --batch-size 32 --epochs 1024
   ```

2. **训练速度慢**
   ```bash
   # 启用AMP加速
   python train.py --n-labeled 4000 --use-amp --epochs 1024
   ```

3. **多进程错误 (Windows)**
   ```bash
   # 设置num-workers为0
   python train.py --n-labeled 4000 --num-workers 0 --epochs 1024
   ```

### 检查点恢复
如果训练中断，可以从检查点恢复：
```bash
# 查看保存的检查点
ls experiments/fixmatch_cifar10_4000_per_class/checkpoints/

# 从检查点恢复训练 (需要修改代码支持)
```

## 实验报告撰写

### 必要内容
1. **算法原理**: FixMatch的核心思想和技术细节
2. **实现细节**: 网络结构、数据增强、训练策略
3. **实验设置**: 参数配置、数据集划分
4. **结果分析**: 不同标注数据量下的性能对比
5. **与其他方法对比**: 与MixMatch、π-Model等的差异

### 结果展示
- 准确率对比表格
- 训练曲线图
- 混淆矩阵
- 每类准确率分析

## 与TorchSSL/USB对比

### 安装USB库
```bash
pip install usb-ssl
```

### 运行USB中的FixMatch
```bash
# 使用USB库的FixMatch实现
python -m usb.train --config configs/fixmatch_cifar10.yaml
```

### 对比分析
比较自实现版本与USB库版本的：
- 准确率差异
- 训练速度
- 内存使用
- 实现细节差异

## 扩展实验

### 消融实验
1. **不同置信度阈值**: 0.9, 0.95, 0.99
2. **不同λ_u值**: 0.5, 1.0, 2.0
3. **不同数据增强策略**: 只用弱增强 vs 弱+强增强

### 其他数据集
- CIFAR-100
- SVHN
- STL-10

## 总结

我们的FixMatch实现已经完全就绪，使用20k训练步数可以在合理时间内完成高质量实验。这种设置在验证算法有效性的同时，大幅缩短了训练时间。

**开始20k steps实验**:
```bash
# 推荐使用专用脚本
python run_20k_experiments.py

# 或使用main.py
python main.py --experiments 40 250 4000 --max-steps 20000 --use-ema --use-amp
```

**预计完成时间**: 2-4小时
**预期结果**: 验证算法有效性，展示不同标注数据量的性能差异

### 实验价值
1. **算法验证**: 证明FixMatch实现正确
2. **效果对比**: 展示半监督学习在不同标注数据量下的效果
3. **时间效率**: 在有限时间内完成高质量实验
4. **学术意义**: 满足课程实验要求，具备实际应用价值

🚀 **祝20k steps实验顺利！**
