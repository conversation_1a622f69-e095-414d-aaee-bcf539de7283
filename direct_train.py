"""
Direct training script for FixMatch - avoids subprocess issues
"""
import os
import sys
import argparse
import time
import logging

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import train_fixmatch

def setup_logging(log_dir):
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'train.log')),
            logging.StreamHandler()
        ]
    )

def run_direct_experiment(n_labeled=40, max_steps=20000):
    """
    Run FixMatch experiment directly without subprocess
    """
    print(f"Direct FixMatch Training - {n_labeled} labeled samples per class")
    print("="*70)
    
    # Create experiment directory
    exp_name = f'fixmatch_cifar10_{n_labeled}_per_class_20k_direct'
    exp_dir = f"experiments/{exp_name}"
    log_dir = f"{exp_dir}/logs"
    checkpoint_dir = f"{exp_dir}/checkpoints"
    
    os.makedirs(exp_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # Setup logging
    setup_logging(log_dir)
    
    # Create arguments object
    class Args:
        def __init__(self):
            self.data_root = './data'
            self.n_labeled = n_labeled
            self.batch_size = 64
            self.mu = 7
            self.epochs = 1024
            self.max_steps = max_steps
            self.lr = 0.03
            self.weight_decay = 5e-4
            self.momentum = 0.9
            self.threshold = 0.95
            self.lambda_u = 1.0
            self.use_ema = True
            self.ema_decay = 0.999
            self.use_amp = True
            self.num_workers = 0
            self.seed = 42
            self.log_dir = log_dir
            self.checkpoint_dir = checkpoint_dir
            self.log_interval = 100
            self.eval_interval = 1000
            self.save_interval = 5000
    
    args = Args()
    
    print(f"Experiment: {exp_name}")
    print(f"Labeled samples per class: {args.n_labeled}")
    print(f"Total labeled samples: {args.n_labeled * 10}")
    print(f"Max training steps: {args.max_steps}")
    print(f"Log directory: {args.log_dir}")
    print(f"Checkpoint directory: {args.checkpoint_dir}")
    print("="*70)
    
    # Run training
    start_time = time.time()
    try:
        best_acc = train_fixmatch(args)
        training_time = time.time() - start_time
        
        print(f"\n{'='*70}")
        print("TRAINING COMPLETED SUCCESSFULLY!")
        print(f"Best test accuracy: {best_acc:.4f}")
        print(f"Training time: {training_time:.2f} seconds ({training_time/60:.1f} minutes)")
        print(f"Model saved in: {args.checkpoint_dir}")
        print(f"{'='*70}")
        
        return {
            'success': True,
            'best_accuracy': best_acc,
            'training_time': training_time,
            'checkpoint_dir': args.checkpoint_dir
        }
        
    except Exception as e:
        training_time = time.time() - start_time
        print(f"\n{'='*70}")
        print("TRAINING FAILED!")
        print(f"Error: {str(e)}")
        print(f"Training time before failure: {training_time:.2f} seconds")
        print(f"{'='*70}")
        
        import traceback
        traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e),
            'training_time': training_time
        }

def main():
    parser = argparse.ArgumentParser(description='Direct FixMatch Training')
    parser.add_argument('--n-labeled', default=40, type=int, help='Number of labeled samples per class')
    parser.add_argument('--max-steps', default=20000, type=int, help='Maximum training steps')
    
    args = parser.parse_args()
    
    result = run_direct_experiment(args.n_labeled, args.max_steps)
    
    if result['success']:
        print(f"\n🎉 Experiment completed successfully!")
        print(f"Best accuracy: {result['best_accuracy']:.4f}")
        print(f"Training time: {result['training_time']/60:.1f} minutes")
    else:
        print(f"\n❌ Experiment failed: {result['error']}")

if __name__ == '__main__':
    main()
