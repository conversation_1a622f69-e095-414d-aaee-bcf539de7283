"""
Main experiment script for FixMatch on CIFAR-10
Runs experiments with different numbers of labeled samples (40, 250, 4000)
"""
import os
import sys
import subprocess
import argparse
import json
import time
from datetime import datetime


def run_experiment(n_labeled, base_args, experiment_name):
    """
    Run a single experiment with specified number of labeled samples
    """
    print(f"\n{'='*60}")
    print(f"Running experiment: {experiment_name}")
    print(f"Number of labeled samples per class: {n_labeled}")
    print(f"Total labeled samples: {n_labeled * 10}")
    print(f"{'='*60}")
    
    # Create experiment-specific directories
    exp_dir = os.path.join('experiments', experiment_name)
    log_dir = os.path.join(exp_dir, 'logs')
    checkpoint_dir = os.path.join(exp_dir, 'checkpoints')
    
    os.makedirs(exp_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # Prepare training command
    cmd = [
        sys.executable, 'train.py',
        '--n-labeled', str(n_labeled),
        '--log-dir', log_dir,
        '--checkpoint-dir', checkpoint_dir
    ]
    
    # Add base arguments
    for key, value in base_args.items():
        if value is not None:
            if isinstance(value, bool):
                if value:
                    cmd.append(f'--{key}')
            else:
                cmd.extend([f'--{key}', str(value)])
    
    print(f"Command: {' '.join(cmd)}")
    
    # Run training
    start_time = time.time()
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Training completed successfully!")
        print("STDOUT:", result.stdout[-1000:])  # Last 1000 characters
    except subprocess.CalledProcessError as e:
        print(f"Training failed with return code {e.returncode}")
        print("STDOUT:", e.stdout[-1000:] if e.stdout else "No stdout")
        print("STDERR:", e.stderr[-1000:] if e.stderr else "No stderr")
        return None
    
    training_time = time.time() - start_time
    
    # Find best checkpoint
    best_checkpoint = os.path.join(checkpoint_dir, 'best_model.pth')
    if not os.path.exists(best_checkpoint):
        print(f"Warning: Best checkpoint not found at {best_checkpoint}")
        return None
    
    # Run evaluation
    eval_dir = os.path.join(exp_dir, 'evaluation')
    eval_cmd = [
        sys.executable, 'evaluate.py',
        '--checkpoint', best_checkpoint,
        '--output-dir', eval_dir,
        '--n-labeled', str(n_labeled)
    ]
    
    print("Running evaluation...")
    try:
        eval_result = subprocess.run(eval_cmd, check=True, capture_output=True, text=True)
        print("Evaluation completed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Evaluation failed with return code {e.returncode}")
        print("STDERR:", e.stderr[-500:] if e.stderr else "No stderr")
        return None
    
    # Load evaluation results
    eval_summary_path = os.path.join(eval_dir, 'evaluation_summary.json')
    if os.path.exists(eval_summary_path):
        with open(eval_summary_path, 'r') as f:
            eval_summary = json.load(f)
        test_accuracy = eval_summary['test_accuracy']
    else:
        print("Warning: Evaluation summary not found")
        test_accuracy = None
    
    return {
        'experiment_name': experiment_name,
        'n_labeled': n_labeled,
        'total_labeled': n_labeled * 10,
        'test_accuracy': test_accuracy,
        'training_time': training_time,
        'checkpoint_path': best_checkpoint,
        'evaluation_dir': eval_dir
    }


def main():
    parser = argparse.ArgumentParser(description='Run FixMatch experiments on CIFAR-10')
    
    # Experiment settings
    parser.add_argument('--experiments', nargs='+', default=['40', '250', '4000'],
                       help='Number of labeled samples per class to experiment with')
    parser.add_argument('--epochs', default=1024, type=int, help='Number of training epochs')
    parser.add_argument('--batch-size', default=64, type=int, help='Batch size')
    parser.add_argument('--lr', default=0.03, type=float, help='Learning rate')
    parser.add_argument('--threshold', default=0.95, type=float, help='Confidence threshold')
    parser.add_argument('--lambda-u', default=1.0, type=float, help='Unsupervised loss weight')
    parser.add_argument('--use-ema', action='store_true', help='Use EMA')
    parser.add_argument('--use-amp', action='store_true', help='Use AMP')
    parser.add_argument('--seed', default=42, type=int, help='Random seed')
    
    # System settings
    parser.add_argument('--num-workers', default=4, type=int, help='Number of workers')
    parser.add_argument('--eval-interval', default=10, type=int, help='Evaluation interval')
    
    args = parser.parse_args()
    
    # Convert experiment list to integers
    n_labeled_list = [int(x) for x in args.experiments]
    
    print("FixMatch CIFAR-10 Experiments")
    print(f"Experiments to run: {n_labeled_list}")
    print(f"Training epochs: {args.epochs}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.lr}")
    print(f"Confidence threshold: {args.threshold}")
    print(f"Lambda_u: {args.lambda_u}")
    print(f"Use EMA: {args.use_ema}")
    print(f"Use AMP: {args.use_amp}")
    print(f"Random seed: {args.seed}")
    
    # Base arguments for training
    base_args = {
        'epochs': args.epochs,
        'batch-size': args.batch_size,
        'lr': args.lr,
        'threshold': args.threshold,
        'lambda-u': args.lambda_u,
        'use-ema': args.use_ema,
        'use-amp': args.use_amp,
        'seed': args.seed,
        'num-workers': args.num_workers,
        'eval-interval': args.eval_interval
    }
    
    # Run experiments
    results = []
    total_start_time = time.time()
    
    for n_labeled in n_labeled_list:
        experiment_name = f'fixmatch_cifar10_{n_labeled}_per_class'
        result = run_experiment(n_labeled, base_args, experiment_name)
        
        if result is not None:
            results.append(result)
            print(f"\nExperiment {experiment_name} completed!")
            print(f"Test Accuracy: {result['test_accuracy']:.4f}")
            print(f"Training Time: {result['training_time']:.2f} seconds")
        else:
            print(f"\nExperiment {experiment_name} failed!")
    
    total_time = time.time() - total_start_time
    
    # Summary
    print(f"\n{'='*80}")
    print("EXPERIMENT SUMMARY")
    print(f"{'='*80}")
    print(f"Total time: {total_time:.2f} seconds ({total_time/3600:.2f} hours)")
    print(f"Completed experiments: {len(results)}/{len(n_labeled_list)}")
    print()
    
    if results:
        print("Results:")
        print(f"{'Labeled/Class':<12} {'Total Labeled':<14} {'Test Accuracy':<14} {'Training Time':<14}")
        print("-" * 60)
        for result in results:
            print(f"{result['n_labeled']:<12} {result['total_labeled']:<14} "
                  f"{result['test_accuracy']:.4f}        {result['training_time']:.2f}s")
        
        # Save summary
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_time': total_time,
            'base_args': base_args,
            'results': results
        }
        
        os.makedirs('experiments', exist_ok=True)
        with open('experiments/summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\nSummary saved to experiments/summary.json")
        
        # Create comparison plot
        try:
            import matplotlib.pyplot as plt
            
            n_labeled_vals = [r['n_labeled'] for r in results]
            accuracies = [r['test_accuracy'] for r in results]
            
            plt.figure(figsize=(10, 6))
            plt.plot(n_labeled_vals, accuracies, 'o-', linewidth=2, markersize=8)
            plt.xlabel('Number of Labeled Samples per Class')
            plt.ylabel('Test Accuracy')
            plt.title('FixMatch Performance on CIFAR-10')
            plt.grid(True, alpha=0.3)
            plt.xscale('log')
            
            # Add value labels
            for x, y in zip(n_labeled_vals, accuracies):
                plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                           xytext=(0,10), ha='center')
            
            plt.tight_layout()
            plt.savefig('experiments/performance_comparison.png', dpi=300, bbox_inches='tight')
            plt.show()
            
            print("Performance comparison plot saved to experiments/performance_comparison.png")
            
        except ImportError:
            print("Matplotlib not available, skipping comparison plot")
    
    else:
        print("No experiments completed successfully.")
    
    print(f"\n{'='*80}")


if __name__ == '__main__':
    main()
