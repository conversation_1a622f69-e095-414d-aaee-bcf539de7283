"""
Evaluation script for FixMatch on CIFAR-10
"""
import os
import argparse
import torch
import torch.nn.functional as F
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

from data.cifar10_ssl import get_cifar10_dataloaders
from models.wideresnet import build_wideresnet


def load_model(checkpoint_path, num_classes=10):
    """Load model from checkpoint"""
    model = build_wideresnet(depth=28, widen_factor=2, dropout=0.0, num_classes=num_classes)
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    return model, checkpoint


def evaluate_model(model, test_loader, device, num_classes=10):
    """Evaluate model on test set"""
    model.eval()
    model = model.to(device)
    
    all_predictions = []
    all_targets = []
    all_probs = []
    total_correct = 0
    total_samples = 0
    
    with torch.no_grad():
        for images, targets in test_loader:
            images = images.to(device)
            targets = targets.to(device)
            
            logits = model(images)
            probs = F.softmax(logits, dim=1)
            predictions = logits.argmax(dim=1)
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
            
            total_correct += (predictions == targets).sum().item()
            total_samples += images.size(0)
    
    accuracy = total_correct / total_samples
    
    return {
        'accuracy': accuracy,
        'predictions': np.array(all_predictions),
        'targets': np.array(all_targets),
        'probabilities': np.array(all_probs)
    }


def plot_confusion_matrix(targets, predictions, class_names, save_path=None):
    """Plot confusion matrix"""
    cm = confusion_matrix(targets, predictions)
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


def plot_class_accuracies(targets, predictions, class_names, save_path=None):
    """Plot per-class accuracies"""
    class_accuracies = []
    for i in range(len(class_names)):
        class_mask = targets == i
        if class_mask.sum() > 0:
            class_acc = (predictions[class_mask] == targets[class_mask]).mean()
            class_accuracies.append(class_acc)
        else:
            class_accuracies.append(0.0)
    
    plt.figure(figsize=(12, 6))
    bars = plt.bar(class_names, class_accuracies)
    plt.title('Per-Class Accuracy')
    plt.xlabel('Class')
    plt.ylabel('Accuracy')
    plt.xticks(rotation=45)
    
    # Add value labels on bars
    for bar, acc in zip(bars, class_accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{acc:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


def analyze_confidence(probabilities, targets, predictions, save_path=None):
    """Analyze model confidence"""
    max_probs = np.max(probabilities, axis=1)
    correct_mask = predictions == targets
    
    # Confidence distribution
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.hist(max_probs[correct_mask], bins=50, alpha=0.7, label='Correct', density=True)
    plt.hist(max_probs[~correct_mask], bins=50, alpha=0.7, label='Incorrect', density=True)
    plt.xlabel('Max Probability')
    plt.ylabel('Density')
    plt.title('Confidence Distribution')
    plt.legend()
    
    # Accuracy vs Confidence
    plt.subplot(1, 3, 2)
    confidence_bins = np.linspace(0, 1, 11)
    bin_accuracies = []
    bin_counts = []
    
    for i in range(len(confidence_bins) - 1):
        mask = (max_probs >= confidence_bins[i]) & (max_probs < confidence_bins[i+1])
        if mask.sum() > 0:
            bin_acc = correct_mask[mask].mean()
            bin_accuracies.append(bin_acc)
            bin_counts.append(mask.sum())
        else:
            bin_accuracies.append(0)
            bin_counts.append(0)
    
    bin_centers = (confidence_bins[:-1] + confidence_bins[1:]) / 2
    plt.plot(bin_centers, bin_accuracies, 'o-')
    plt.plot([0, 1], [0, 1], 'r--', alpha=0.5, label='Perfect Calibration')
    plt.xlabel('Confidence')
    plt.ylabel('Accuracy')
    plt.title('Reliability Diagram')
    plt.legend()
    
    # Sample counts per bin
    plt.subplot(1, 3, 3)
    plt.bar(bin_centers, bin_counts, width=0.08)
    plt.xlabel('Confidence')
    plt.ylabel('Count')
    plt.title('Sample Distribution')
    
    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    return {
        'avg_confidence': max_probs.mean(),
        'correct_confidence': max_probs[correct_mask].mean(),
        'incorrect_confidence': max_probs[~correct_mask].mean()
    }


def main():
    parser = argparse.ArgumentParser(description='Evaluate FixMatch model on CIFAR-10')
    parser.add_argument('--checkpoint', required=True, type=str, help='Path to model checkpoint')
    parser.add_argument('--data-root', default='./data', type=str, help='Data directory')
    parser.add_argument('--batch-size', default=128, type=int, help='Batch size for evaluation')
    parser.add_argument('--num-workers', default=4, type=int, help='Number of data loading workers')
    parser.add_argument('--output-dir', default='./evaluation_results', type=str, help='Output directory for plots')
    parser.add_argument('--n-labeled', default=4000, type=int, help='Number of labeled samples (for data loading)')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Load model
    print(f'Loading model from {args.checkpoint}')
    model, checkpoint = load_model(args.checkpoint)
    print(f'Model loaded from epoch {checkpoint.get("epoch", "unknown")}')
    print(f'Best accuracy during training: {checkpoint.get("best_acc", "unknown"):.4f}')
    
    # Load test data
    print('Loading test data...')
    _, _, test_loader = get_cifar10_dataloaders(
        root=args.data_root,
        n_labeled=args.n_labeled,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )
    
    # CIFAR-10 class names
    class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer',
                   'dog', 'frog', 'horse', 'ship', 'truck']
    
    # Evaluate model
    print('Evaluating model...')
    results = evaluate_model(model, test_loader, device)
    
    print(f'Test Accuracy: {results["accuracy"]:.4f}')
    
    # Classification report
    print('\nClassification Report:')
    print(classification_report(results['targets'], results['predictions'],
                              target_names=class_names, digits=4))
    
    # Plot confusion matrix
    print('Generating confusion matrix...')
    plot_confusion_matrix(results['targets'], results['predictions'], class_names,
                         save_path=os.path.join(args.output_dir, 'confusion_matrix.png'))
    
    # Plot per-class accuracies
    print('Generating per-class accuracy plot...')
    plot_class_accuracies(results['targets'], results['predictions'], class_names,
                         save_path=os.path.join(args.output_dir, 'class_accuracies.png'))
    
    # Analyze confidence
    print('Analyzing model confidence...')
    confidence_stats = analyze_confidence(results['probabilities'], results['targets'],
                                        results['predictions'],
                                        save_path=os.path.join(args.output_dir, 'confidence_analysis.png'))
    
    print(f'Average confidence: {confidence_stats["avg_confidence"]:.4f}')
    print(f'Confidence on correct predictions: {confidence_stats["correct_confidence"]:.4f}')
    print(f'Confidence on incorrect predictions: {confidence_stats["incorrect_confidence"]:.4f}')
    
    # Save detailed results
    results_summary = {
        'test_accuracy': results['accuracy'],
        'confidence_stats': confidence_stats,
        'checkpoint_info': {
            'epoch': checkpoint.get('epoch', 'unknown'),
            'best_acc': checkpoint.get('best_acc', 'unknown')
        }
    }
    
    import json
    with open(os.path.join(args.output_dir, 'evaluation_summary.json'), 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f'Evaluation completed! Results saved to {args.output_dir}')


if __name__ == '__main__':
    main()
