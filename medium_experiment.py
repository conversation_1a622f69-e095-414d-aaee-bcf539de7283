"""
Medium-scale experiment script for FixMatch
Run experiments with moderate epochs to demonstrate functionality
"""
import os
import sys
import subprocess
import time
import json

def run_medium_experiment():
    """
    Run medium-scale experiments with 50 epochs
    """
    print("FixMatch Medium-Scale Experiment")
    print("="*60)
    print("Running experiments with 50 epochs to demonstrate functionality")
    print("This will take approximately 30-60 minutes depending on hardware")
    print("="*60)
    
    # Experiments with different labeled sample counts
    experiments = [
        {'n_labeled': 40, 'epochs': 50, 'name': 'fixmatch_cifar10_40_per_class_50ep'},
        {'n_labeled': 250, 'epochs': 50, 'name': 'fixmatch_cifar10_250_per_class_50ep'},
        {'n_labeled': 4000, 'epochs': 50, 'name': 'fixmatch_cifar10_4000_per_class_50ep'}
    ]
    
    results = []
    total_start_time = time.time()
    
    for i, exp in enumerate(experiments):
        print(f"\n{'='*60}")
        print(f"Running experiment {i+1}/3: {exp['name']}")
        print(f"Labeled samples per class: {exp['n_labeled']}")
        print(f"Total labeled samples: {exp['n_labeled'] * 10}")
        print(f"Epochs: {exp['epochs']}")
        print(f"{'='*60}")
        
        # Create directories
        exp_dir = f"experiments/{exp['name']}"
        log_dir = f"{exp_dir}/logs"
        checkpoint_dir = f"{exp_dir}/checkpoints"
        
        os.makedirs(exp_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Prepare command with optimized settings
        cmd = [
            sys.executable, 'train.py',
            '--n-labeled', str(exp['n_labeled']),
            '--epochs', str(exp['epochs']),
            '--batch-size', '64',
            '--lr', '0.03',
            '--threshold', '0.95',
            '--lambda-u', '1.0',
            '--eval-interval', '5',  # Evaluate every 5 epochs
            '--log-dir', log_dir,
            '--checkpoint-dir', checkpoint_dir,
            '--num-workers', '0',  # Avoid multiprocessing issues
            '--use-ema',
            '--use-amp'  # Use automatic mixed precision for speed
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # Run training
        start_time = time.time()
        try:
            print("Starting training...")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            training_time = time.time() - start_time
            print(f"✓ Training completed in {training_time:.2f} seconds ({training_time/60:.1f} minutes)")
            
            # Check if best model exists
            best_model_path = os.path.join(checkpoint_dir, 'best_model.pth')
            if os.path.exists(best_model_path):
                print(f"✓ Best model saved at {best_model_path}")
                
                # Try to extract accuracy from output
                output_lines = result.stdout.split('\n')
                best_acc = 0.0
                for line in output_lines:
                    if 'Best test accuracy:' in line:
                        try:
                            best_acc = float(line.split(':')[-1].strip())
                        except:
                            pass
                    elif 'Test Acc:' in line:
                        try:
                            # Extract from "Test Acc: 0.xxxx" format
                            acc_str = line.split('Test Acc:')[-1].strip()
                            best_acc = max(best_acc, float(acc_str))
                        except:
                            pass
                
                print(f"✓ Best test accuracy: {best_acc:.4f}")
                
                results.append({
                    'name': exp['name'],
                    'n_labeled': exp['n_labeled'],
                    'total_labeled': exp['n_labeled'] * 10,
                    'epochs': exp['epochs'],
                    'training_time': training_time,
                    'best_accuracy': best_acc,
                    'status': 'success',
                    'model_path': best_model_path
                })
            else:
                print("✗ Best model not found")
                results.append({
                    'name': exp['name'],
                    'n_labeled': exp['n_labeled'],
                    'total_labeled': exp['n_labeled'] * 10,
                    'epochs': exp['epochs'],
                    'training_time': training_time,
                    'best_accuracy': 0.0,
                    'status': 'no_model',
                    'model_path': None
                })
                
        except subprocess.TimeoutExpired:
            print("✗ Training timed out (1 hour limit)")
            results.append({
                'name': exp['name'],
                'n_labeled': exp['n_labeled'],
                'total_labeled': exp['n_labeled'] * 10,
                'epochs': exp['epochs'],
                'training_time': 3600,
                'best_accuracy': 0.0,
                'status': 'timeout',
                'model_path': None
            })
        except subprocess.CalledProcessError as e:
            print(f"✗ Training failed with return code {e.returncode}")
            print("STDERR:", e.stderr[-500:] if e.stderr else "No stderr")
            results.append({
                'name': exp['name'],
                'n_labeled': exp['n_labeled'],
                'total_labeled': exp['n_labeled'] * 10,
                'epochs': exp['epochs'],
                'training_time': 0,
                'best_accuracy': 0.0,
                'status': 'failed',
                'model_path': None
            })
    
    total_time = time.time() - total_start_time
    
    # Summary
    print("\n" + "="*80)
    print("EXPERIMENT SUMMARY")
    print("="*80)
    print(f"Total time: {total_time:.2f} seconds ({total_time/3600:.2f} hours)")
    print(f"Completed experiments: {len([r for r in results if r['status'] == 'success'])}/{len(results)}")
    print()
    
    if results:
        print("Results:")
        print(f"{'Labeled/Class':<12} {'Total Labeled':<14} {'Test Accuracy':<14} {'Training Time':<14} {'Status':<10}")
        print("-" * 80)
        for result in results:
            print(f"{result['n_labeled']:<12} {result['total_labeled']:<14} "
                  f"{result['best_accuracy']:.4f}        {result['training_time']:.2f}s        {result['status']:<10}")
        
        # Save detailed results
        summary = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_time': total_time,
            'experiment_type': 'medium_scale_50_epochs',
            'results': results
        }
        
        os.makedirs('experiments', exist_ok=True)
        with open('experiments/medium_experiment_results.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\nDetailed results saved to experiments/medium_experiment_results.json")
        
        # Create comparison plot if matplotlib is available
        try:
            import matplotlib.pyplot as plt
            
            successful_results = [r for r in results if r['status'] == 'success' and r['best_accuracy'] > 0]
            if successful_results:
                n_labeled_vals = [r['n_labeled'] for r in successful_results]
                accuracies = [r['best_accuracy'] for r in successful_results]
                
                plt.figure(figsize=(10, 6))
                plt.plot(n_labeled_vals, accuracies, 'o-', linewidth=2, markersize=8)
                plt.xlabel('Number of Labeled Samples per Class')
                plt.ylabel('Test Accuracy')
                plt.title('FixMatch Performance on CIFAR-10 (50 epochs)')
                plt.grid(True, alpha=0.3)
                plt.xscale('log')
                
                # Add value labels
                for x, y in zip(n_labeled_vals, accuracies):
                    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                               xytext=(0,10), ha='center')
                
                plt.tight_layout()
                plt.savefig('experiments/medium_experiment_performance.png', dpi=300, bbox_inches='tight')
                print("Performance comparison plot saved to experiments/medium_experiment_performance.png")
                
        except ImportError:
            print("Matplotlib not available, skipping comparison plot")
    
    else:
        print("No experiments completed successfully.")
    
    print(f"\n{'='*80}")
    print("Medium-scale experiment completed!")
    print("For full results as specified in the assignment, run:")
    print("python main.py --experiments 40 250 4000 --epochs 1024 --use-ema --use-amp")
    print(f"{'='*80}")
    
    return results

if __name__ == '__main__':
    run_medium_experiment()
