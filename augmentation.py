"""
Data augmentation strategies for FixMatch
Implements weak and strong augmentation as described in the paper
"""
import torch
import torchvision.transforms as transforms
from PIL import Image, ImageOps, ImageEnhance
import numpy as np
import random


class RandAugmentMC(object):
    """
    RandAugment for FixMatch - Strong augmentation
    """
    def __init__(self, n, m):
        self.n = n
        self.m = m      # [0, 30]
        self.augment_list = augment_list()

    def __call__(self, img):
        ops = random.choices(self.augment_list, k=self.n)
        for op, minval, maxval in ops:
            val = (float(self.m) / 30) * float(maxval - minval) + minval
            img = op(img, val)
        return img


def augment_list():
    """
    List of augmentation operations for RandAugment
    """
    l = [
        (AutoContrast, 0, 1),
        (Brightness, 0.1, 1.9),
        (Color, 0.1, 1.9),
        (Contrast, 0.1, 1.9),
        (Equalize, 0, 1),
        (Invert, 0, 1),
        (<PERSON><PERSON><PERSON>, 0, 4),
        (<PERSON>otate, -30, 30),
        (<PERSON><PERSON>, 0.1, 1.9),
        (ShearX, -0.3, 0.3),
        (ShearY, -0.3, 0.3),
        (Solarize, 0, 256),
        (TranslateX, -0.3, 0.3),
        (TranslateY, -0.3, 0.3)
    ]
    return l


def AutoContrast(img, _):
    return ImageOps.autocontrast(img)


def Brightness(img, v):
    assert v >= 0.0
    return ImageEnhance.Brightness(img).enhance(v)


def Color(img, v):
    assert v >= 0.0
    return ImageEnhance.Color(img).enhance(v)


def Contrast(img, v):
    assert v >= 0.0
    return ImageEnhance.Contrast(img).enhance(v)


def Equalize(img, _):
    return ImageOps.equalize(img)


def Invert(img, _):
    return ImageOps.invert(img)


def Posterize(img, v):
    v = int(v)
    v = max(1, v)
    return ImageOps.posterize(img, v)


def Rotate(img, v):
    return img.rotate(v)


def Sharpness(img, v):
    assert v >= 0.0
    return ImageEnhance.Sharpness(img).enhance(v)


def ShearX(img, v):
    return img.transform(img.size, Image.AFFINE, (1, v, 0, 0, 1, 0))


def ShearY(img, v):
    return img.transform(img.size, Image.AFFINE, (1, 0, 0, v, 1, 0))


def Solarize(img, v):
    assert 0 <= v <= 256
    return ImageOps.solarize(img, v)


def TranslateX(img, v):
    v = v * img.size[0]
    return img.transform(img.size, Image.AFFINE, (1, 0, v, 0, 1, 0))


def TranslateY(img, v):
    v = v * img.size[1]
    return img.transform(img.size, Image.AFFINE, (1, 0, 0, 0, 1, v))


def get_weak_augmentation():
    """
    Weak augmentation: RandomHorizontalFlip + RandomCrop with padding
    """
    return transforms.Compose([
        transforms.RandomHorizontalFlip(),
        transforms.RandomCrop(size=32,
                              padding=int(32*0.125),
                              padding_mode='reflect')
    ])


def get_strong_augmentation():
    """
    Strong augmentation: RandAugment + Cutout
    """
    return transforms.Compose([
        transforms.RandomHorizontalFlip(),
        transforms.RandomCrop(size=32,
                              padding=int(32*0.125),
                              padding_mode='reflect'),
        RandAugmentMC(n=2, m=10),
        Cutout(n_holes=1, length=16)
    ])


class Cutout(object):
    """
    Cutout augmentation
    """
    def __init__(self, n_holes, length):
        self.n_holes = n_holes
        self.length = length

    def __call__(self, img):
        """
        Args:
            img (Tensor): Tensor image of size (C, H, W).
        Returns:
            Tensor: Image with n_holes of dimension length x length cut out of it.
        """
        h = img.size(1)
        w = img.size(2)

        mask = np.ones((h, w), np.float32)

        for n in range(self.n_holes):
            y = np.random.randint(h)
            x = np.random.randint(w)

            y1 = np.clip(y - self.length // 2, 0, h)
            y2 = np.clip(y + self.length // 2, 0, h)
            x1 = np.clip(x - self.length // 2, 0, w)
            x2 = np.clip(x + self.length // 2, 0, w)

            mask[y1: y2, x1: x2] = 0.

        mask = torch.from_numpy(mask)
        mask = mask.expand_as(img)
        img = img * mask

        return img


class TransformFixMatch(object):
    """
    Transform for FixMatch that returns both weak and strong augmented versions
    """
    def __init__(self, weak_transform, strong_transform):
        self.weak = weak_transform
        self.strong = strong_transform

    def __call__(self, x):
        weak = self.weak(x)
        strong = self.strong(x)
        return weak, strong


def get_cifar10_transforms():
    """
    Get CIFAR-10 transforms for FixMatch
    """
    # Normalization values for CIFAR-10
    cifar10_mean = (0.4914, 0.4822, 0.4465)
    cifar10_std = (0.2471, 0.2435, 0.2616)

    # Transform for labeled data (weak augmentation)
    transform_labeled = transforms.Compose([
        get_weak_augmentation(),
        transforms.ToTensor(),
        transforms.Normalize(mean=cifar10_mean, std=cifar10_std)
    ])

    # Transform for unlabeled data - returns both weak and strong augmented versions
    transform_unlabeled = TransformFixMatch(
        weak_transform=transforms.Compose([
            get_weak_augmentation(),
            transforms.ToTensor(),
            transforms.Normalize(mean=cifar10_mean, std=cifar10_std)
        ]),
        strong_transform=transforms.Compose([
            get_strong_augmentation(),
            transforms.ToTensor(),
            transforms.Normalize(mean=cifar10_mean, std=cifar10_std)
        ])
    )

    # Transform for test data
    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=cifar10_mean, std=cifar10_std)
    ])

    return transform_labeled, transform_unlabeled, transform_test
