"""
FixMatch: Simplifying Semi-Supervised Learning with Consistency and Confidence
Implementation of the FixMatch algorithm for semi-supervised learning
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.cuda.amp import autocast, GradScaler


class FixMatch:
    """
    FixMatch algorithm implementation
    """
    def __init__(self, model, optimizer, scheduler=None, threshold=0.95, 
                 lambda_u=1.0, use_amp=True, device='cuda'):
        self.model = model
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.threshold = threshold  # Confidence threshold for pseudo-labels
        self.lambda_u = lambda_u    # Weight for unsupervised loss
        self.use_amp = use_amp      # Automatic Mixed Precision
        self.device = device
        
        if self.use_amp:
            self.scaler = GradScaler()
        
        # Loss functions
        self.criterion_labeled = nn.CrossEntropyLoss()
        self.criterion_unlabeled = nn.CrossEntropyLoss(reduction='none')
    
    def train_step(self, labeled_batch, unlabeled_batch):
        """
        Single training step for FixMatch
        
        Args:
            labeled_batch: (images, labels) for labeled data
            unlabeled_batch: ((weak_aug, strong_aug), _) for unlabeled data
        
        Returns:
            Dictionary containing loss components and metrics
        """
        self.model.train()
        
        # Unpack batches
        labeled_images, labeled_targets = labeled_batch
        (unlabeled_weak, unlabeled_strong), _ = unlabeled_batch
        
        # Move to device
        labeled_images = labeled_images.to(self.device)
        labeled_targets = labeled_targets.to(self.device)
        unlabeled_weak = unlabeled_weak.to(self.device)
        unlabeled_strong = unlabeled_strong.to(self.device)
        
        labeled_batch_size = labeled_images.shape[0]
        unlabeled_batch_size = unlabeled_weak.shape[0]

        # Combine all images for efficient forward pass
        all_images = torch.cat([labeled_images, unlabeled_weak, unlabeled_strong])

        with autocast(enabled=self.use_amp):
            # Forward pass
            all_logits = self.model(all_images)

            # Split logits
            labeled_logits = all_logits[:labeled_batch_size]
            unlabeled_weak_logits = all_logits[labeled_batch_size:labeled_batch_size+unlabeled_batch_size]
            unlabeled_strong_logits = all_logits[labeled_batch_size+unlabeled_batch_size:]
            
            # Supervised loss
            loss_labeled = self.criterion_labeled(labeled_logits, labeled_targets)
            
            # Generate pseudo-labels from weak augmentation
            with torch.no_grad():
                pseudo_probs = torch.softmax(unlabeled_weak_logits, dim=-1)
                max_probs, pseudo_labels = torch.max(pseudo_probs, dim=-1)
                mask = max_probs.ge(self.threshold).float()
            
            # Unsupervised loss (only for confident predictions)
            loss_unlabeled = (self.criterion_unlabeled(unlabeled_strong_logits, pseudo_labels) * mask).mean()
            
            # Total loss
            loss = loss_labeled + self.lambda_u * loss_unlabeled
        
        # Backward pass
        self.optimizer.zero_grad()
        if self.use_amp:
            self.scaler.scale(loss).backward()
            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            loss.backward()
            self.optimizer.step()
        
        if self.scheduler is not None:
            self.scheduler.step()
        
        # Calculate metrics
        with torch.no_grad():
            # Labeled accuracy
            labeled_pred = labeled_logits.argmax(dim=-1)
            labeled_acc = (labeled_pred == labeled_targets).float().mean()
            
            # Pseudo-label statistics
            mask_prob = mask.mean()
            
        return {
            'loss': loss.item(),
            'loss_labeled': loss_labeled.item(),
            'loss_unlabeled': loss_unlabeled.item(),
            'labeled_acc': labeled_acc.item(),
            'mask_prob': mask_prob.item(),
            'pseudo_acc': (pseudo_labels == labeled_targets[:len(pseudo_labels)]).float().mean().item() if len(pseudo_labels) <= len(labeled_targets) else 0.0
        }
    
    def evaluate(self, test_loader):
        """
        Evaluate model on test set
        
        Args:
            test_loader: DataLoader for test data
        
        Returns:
            Dictionary containing evaluation metrics
        """
        self.model.eval()
        total_loss = 0
        total_correct = 0
        total_samples = 0
        
        with torch.no_grad():
            for images, targets in test_loader:
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                with autocast(enabled=self.use_amp):
                    logits = self.model(images)
                    loss = self.criterion_labeled(logits, targets)
                
                total_loss += loss.item() * images.size(0)
                pred = logits.argmax(dim=-1)
                total_correct += (pred == targets).sum().item()
                total_samples += images.size(0)
        
        avg_loss = total_loss / total_samples
        accuracy = total_correct / total_samples
        
        return {
            'test_loss': avg_loss,
            'test_acc': accuracy
        }


def interleave(x, size):
    """
    Interleave function for efficient batch processing
    """
    s = list(x.shape)
    return x.reshape([-1, size] + s[1:]).transpose(0, 1).reshape([-1] + s[1:])


def de_interleave(x, size):
    """
    De-interleave function for efficient batch processing
    """
    s = list(x.shape)
    return x.reshape([size, -1] + s[1:]).transpose(0, 1).reshape([-1] + s[1:])


class EMA:
    """
    Exponential Moving Average for model parameters
    """
    def __init__(self, model, decay=0.999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        
        # Initialize shadow parameters
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
    
    def update(self):
        """Update EMA parameters"""
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()
    
    def apply_shadow(self):
        """Apply EMA parameters to model"""
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                self.backup[name] = param.data
                param.data = self.shadow[name]
    
    def restore(self):
        """Restore original parameters"""
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.backup
                param.data = self.backup[name]
        self.backup = {}


def create_model_and_optimizer(num_classes=10, lr=0.03, weight_decay=5e-4, momentum=0.9):
    """
    Create model and optimizer for FixMatch
    """
    from models.wideresnet import build_wideresnet
    
    model = build_wideresnet(depth=28, widen_factor=2, dropout=0.0, num_classes=num_classes)
    
    # No decay for bias and batch norm parameters
    no_decay = ['bias', 'bn']
    grouped_parameters = [
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for nd in no_decay)],
         'weight_decay': weight_decay},
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in no_decay)],
         'weight_decay': 0.0}
    ]
    
    optimizer = torch.optim.SGD(grouped_parameters, lr=lr, momentum=momentum, nesterov=True)
    
    return model, optimizer


def get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps, 
                                   num_cycles=7./16., last_epoch=-1):
    """
    Cosine learning rate schedule with warmup
    """
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        no_progress = float(current_step - num_warmup_steps) / \
            float(max(1, num_training_steps - num_warmup_steps))
        return max(0., math.cos(math.pi * num_cycles * no_progress))
    
    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda, last_epoch)


import math
