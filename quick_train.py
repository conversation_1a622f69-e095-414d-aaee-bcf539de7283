"""
Quick training script to test FixMatch implementation
"""
import torch
import torch.nn as nn
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader, Subset
import numpy as np

from models.wideresnet import build_wideresnet
from fixmatch import FixMatch, create_model_and_optimizer


def get_simple_cifar10_loaders(n_labeled=40, batch_size=16):
    """
    Simple CIFAR-10 data loaders for testing
    """
    # Simple transforms
    transform_train = transforms.Compose([
        transforms.RandomHorizontalFlip(),
        transforms.RandomCrop(32, padding=4),
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2471, 0.2435, 0.2616))
    ])
    
    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2471, 0.2435, 0.2616))
    ])
    
    # Load datasets
    trainset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform_train)
    testset = torchvision.datasets.CIFAR10(root='./data', train=False, download=True, transform=transform_test)
    
    # Create labeled subset
    np.random.seed(42)
    labels = np.array(trainset.targets)
    labeled_idx = []
    
    for i in range(10):  # 10 classes
        idx = np.where(labels == i)[0]
        np.random.shuffle(idx)
        labeled_idx.extend(idx[:n_labeled])
    
    labeled_subset = Subset(trainset, labeled_idx)
    
    # Create data loaders
    labeled_loader = DataLoader(labeled_subset, batch_size=batch_size, shuffle=True, num_workers=0)
    test_loader = DataLoader(testset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    return labeled_loader, test_loader


def quick_train():
    """
    Quick training test
    """
    print("Quick FixMatch Training Test")
    print("="*40)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # Data
    labeled_loader, test_loader = get_simple_cifar10_loaders(n_labeled=4, batch_size=8)
    print(f"Labeled samples: {len(labeled_loader.dataset)}")
    print(f"Test samples: {len(test_loader.dataset)}")
    
    # Model
    model, optimizer = create_model_and_optimizer(num_classes=10, lr=0.01)
    model = model.to(device)
    
    # FixMatch (simplified - only supervised training)
    criterion = nn.CrossEntropyLoss()
    
    # Training loop
    model.train()
    print("\nTraining for 5 epochs...")
    
    for epoch in range(5):
        total_loss = 0
        total_correct = 0
        total_samples = 0
        
        for batch_idx, (images, targets) in enumerate(labeled_loader):
            images, targets = images.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            pred = outputs.argmax(dim=1)
            total_correct += (pred == targets).sum().item()
            total_samples += images.size(0)

            if batch_idx % 10 == 0:
                print(f'  Batch {batch_idx}: Loss={loss.item():.4f}, Acc={total_correct/total_samples:.4f}')
        
        avg_loss = total_loss / len(labeled_loader)
        avg_acc = total_correct / total_samples
        print(f'Epoch {epoch+1}: Loss={avg_loss:.4f}, Acc={avg_acc:.4f}')
    
    # Test
    model.eval()
    test_correct = 0
    test_total = 0
    
    with torch.no_grad():
        for images, targets in test_loader:
            images, targets = images.to(device), targets.to(device)
            outputs = model(images)
            pred = outputs.argmax(dim=1)
            test_correct += (pred == targets).sum().item()
            test_total += images.size(0)
    
    test_acc = test_correct / test_total
    print(f'\nTest Accuracy: {test_acc:.4f}')
    
    print("\n🎉 Quick training test completed!")
    return test_acc


if __name__ == '__main__':
    quick_train()
