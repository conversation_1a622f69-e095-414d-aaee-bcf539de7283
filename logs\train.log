2025-06-10 19:44:12,334 - INFO - Arguments:
2025-06-10 19:44:12,336 - INFO -   data_root: ./data
2025-06-10 19:44:12,336 - INFO -   n_labeled: 40
2025-06-10 19:44:12,336 - INFO -   batch_size: 16
2025-06-10 19:44:12,337 - INFO -   mu: 7
2025-06-10 19:44:12,337 - INFO -   epochs: 1024
2025-06-10 19:44:12,337 - INFO -   max_steps: 100
2025-06-10 19:44:12,337 - INFO -   lr: 0.03
2025-06-10 19:44:12,337 - INFO -   weight_decay: 0.0005
2025-06-10 19:44:12,337 - INFO -   momentum: 0.9
2025-06-10 19:44:12,338 - INFO -   threshold: 0.95
2025-06-10 19:44:12,338 - INFO -   lambda_u: 1.0
2025-06-10 19:44:12,338 - INFO -   use_ema: True
2025-06-10 19:44:12,338 - INFO -   ema_decay: 0.999
2025-06-10 19:44:12,338 - INFO -   use_amp: False
2025-06-10 19:44:12,339 - INFO -   num_workers: 0
2025-06-10 19:44:12,340 - INFO -   seed: 42
2025-06-10 19:44:12,340 - INFO -   log_dir: ./logs
2025-06-10 19:44:12,340 - INFO -   checkpoint_dir: ./checkpoints
2025-06-10 19:44:12,340 - INFO -   log_interval: 100
2025-06-10 19:44:12,340 - INFO -   eval_interval: 50
2025-06-10 19:44:12,340 - INFO -   save_interval: 100
2025-06-10 19:44:12,340 - INFO - Using device: cuda
2025-06-10 19:44:12,343 - INFO - Creating data loaders with 40 labeled samples per class
2025-06-10 19:44:15,726 - INFO - Labeled samples: 400
2025-06-10 19:44:15,726 - INFO - Unlabeled samples: 49600
2025-06-10 19:44:15,727 - INFO - Test samples: 10000
2025-06-10 19:44:15,935 - INFO - Using max_steps=100, adjusted epochs to 4
2025-06-10 19:44:15,938 - INFO - Starting training...
2025-06-10 19:44:25,247 - INFO - Epoch 1/4 - Time: 9.31s
2025-06-10 19:44:25,247 - INFO - Loss: 2.3981, L_sup: 2.3944, L_unsup: 0.0037
2025-06-10 19:44:25,248 - INFO - Labeled Acc: 0.1025, Mask Prob: 0.0036
2025-06-10 19:44:33,729 - INFO - Epoch 2/4 - Time: 8.48s
2025-06-10 19:44:33,729 - INFO - Loss: 2.2799, L_sup: 2.2797, L_unsup: 0.0002
2025-06-10 19:44:33,730 - INFO - Labeled Acc: 0.1725, Mask Prob: 0.0004
2025-06-10 19:44:42,157 - INFO - Epoch 3/4 - Time: 8.43s
2025-06-10 19:44:42,157 - INFO - Loss: 2.0572, L_sup: 2.0572, L_unsup: 0.0000
2025-06-10 19:44:42,158 - INFO - Labeled Acc: 0.2750, Mask Prob: 0.0000
2025-06-10 19:44:51,186 - INFO - Reached max_steps=100 at epoch 4, batch 24
2025-06-10 19:44:51,186 - INFO - Epoch 4/4 - Time: 9.03s
2025-06-10 19:44:51,187 - INFO - Loss: 1.9148, L_sup: 1.9148, L_unsup: 0.0000
2025-06-10 19:44:51,187 - INFO - Labeled Acc: 0.2825, Mask Prob: 0.0000
2025-06-10 19:44:51,187 - INFO - Training completed! Best test accuracy: 0.0000
2025-06-11 00:54:22,730 - INFO - Arguments:
2025-06-11 00:54:22,734 - INFO -   data_root: ./data
2025-06-11 00:54:22,734 - INFO -   n_labeled: 4000
2025-06-11 00:54:22,734 - INFO -   batch_size: 64
2025-06-11 00:54:22,734 - INFO -   mu: 7
2025-06-11 00:54:22,734 - INFO -   epochs: 1024
2025-06-11 00:54:22,734 - INFO -   max_steps: 20000
2025-06-11 00:54:22,735 - INFO -   lr: 0.03
2025-06-11 00:54:22,735 - INFO -   weight_decay: 0.0005
2025-06-11 00:54:22,735 - INFO -   momentum: 0.9
2025-06-11 00:54:22,735 - INFO -   threshold: 0.95
2025-06-11 00:54:22,735 - INFO -   lambda_u: 1.0
2025-06-11 00:54:22,735 - INFO -   use_ema: True
2025-06-11 00:54:22,735 - INFO -   ema_decay: 0.999
2025-06-11 00:54:22,736 - INFO -   use_amp: True
2025-06-11 00:54:22,736 - INFO -   num_workers: 4
2025-06-11 00:54:22,736 - INFO -   seed: 42
2025-06-11 00:54:22,736 - INFO -   log_dir: ./logs
2025-06-11 00:54:22,736 - INFO -   checkpoint_dir: ./checkpoints
2025-06-11 00:54:22,736 - INFO -   log_interval: 100
2025-06-11 00:54:22,736 - INFO -   eval_interval: 10
2025-06-11 00:54:22,736 - INFO -   save_interval: 100
2025-06-11 00:54:22,737 - INFO - Using device: cuda
2025-06-11 00:54:22,742 - INFO - Creating data loaders with 4000 labeled samples per class
2025-06-11 00:54:26,151 - INFO - Labeled samples: 40000
2025-06-11 00:54:26,152 - INFO - Unlabeled samples: 10000
2025-06-11 00:54:26,152 - INFO - Test samples: 10000
2025-06-11 00:54:26,332 - INFO - Using max_steps=20000, adjusted epochs to 32
2025-06-11 00:54:26,337 - INFO - Starting training...
