"""
Test script to verify FixMatch implementation
"""
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader

# Test imports
try:
    from models.wideresnet import build_wideresnet
    from data.cifar10_ssl import get_cifar10_dataloaders
    from fixmatch import FixMatch, create_model_and_optimizer
    from augmentation import get_cifar10_transforms
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    exit(1)


def test_model():
    """Test WideResNet model"""
    print("\n" + "="*50)
    print("Testing WideResNet Model")
    print("="*50)
    
    model = build_wideresnet(depth=28, widen_factor=2, dropout=0.0, num_classes=10)
    print(f"✓ Model created successfully")
    
    # Test forward pass
    x = torch.randn(4, 3, 32, 32)
    with torch.no_grad():
        output = model(x)
    
    print(f"✓ Forward pass successful")
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {output.shape}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"  Total parameters: {total_params:,}")
    print(f"  Trainable parameters: {trainable_params:,}")


def test_data_loading():
    """Test data loading"""
    print("\n" + "="*50)
    print("Testing Data Loading")
    print("="*50)
    
    try:
        # Test with small dataset for quick testing
        labeled_loader, unlabeled_loader, test_loader = get_cifar10_dataloaders(
            root='./data',
            n_labeled=4,  # 4 samples per class = 40 total
            batch_size=8,
            mu=2,
            num_workers=0  # Avoid multiprocessing issues in testing
        )
        print("✓ Data loaders created successfully")
        
        print(f"  Labeled dataset size: {len(labeled_loader.dataset)}")
        print(f"  Unlabeled dataset size: {len(unlabeled_loader.dataset)}")
        print(f"  Test dataset size: {len(test_loader.dataset)}")
        
        # Test labeled data loading
        labeled_batch = next(iter(labeled_loader))
        labeled_images, labeled_targets = labeled_batch
        print(f"✓ Labeled batch loaded")
        print(f"  Labeled images shape: {labeled_images.shape}")
        print(f"  Labeled targets shape: {labeled_targets.shape}")
        
        # Test unlabeled data loading
        unlabeled_batch = next(iter(unlabeled_loader))
        (unlabeled_weak, unlabeled_strong), _ = unlabeled_batch
        print(f"✓ Unlabeled batch loaded")
        print(f"  Weak augmentation shape: {unlabeled_weak.shape}")
        print(f"  Strong augmentation shape: {unlabeled_strong.shape}")
        
        # Test test data loading
        test_batch = next(iter(test_loader))
        test_images, test_targets = test_batch
        print(f"✓ Test batch loaded")
        print(f"  Test images shape: {test_images.shape}")
        print(f"  Test targets shape: {test_targets.shape}")
        
    except Exception as e:
        print(f"✗ Data loading error: {e}")
        return False
    
    return True


def test_augmentation():
    """Test data augmentation"""
    print("\n" + "="*50)
    print("Testing Data Augmentation")
    print("="*50)
    
    try:
        transform_labeled, transform_unlabeled, transform_test = get_cifar10_transforms()
        print("✓ Transforms created successfully")
        
        # Create dummy PIL image
        from PIL import Image
        dummy_image = Image.new('RGB', (32, 32), color='red')
        
        # Test labeled transform
        labeled_tensor = transform_labeled(dummy_image)
        print(f"✓ Labeled transform: {labeled_tensor.shape}")
        
        # Test unlabeled transform
        unlabeled_result = transform_unlabeled(dummy_image)
        weak_tensor, strong_tensor = unlabeled_result
        print(f"✓ Unlabeled transform - Weak: {weak_tensor.shape}, Strong: {strong_tensor.shape}")
        
        # Test test transform
        test_tensor = transform_test(dummy_image)
        print(f"✓ Test transform: {test_tensor.shape}")
        
    except Exception as e:
        print(f"✗ Augmentation error: {e}")
        return False
    
    return True


def test_fixmatch():
    """Test FixMatch algorithm"""
    print("\n" + "="*50)
    print("Testing FixMatch Algorithm")
    print("="*50)
    
    try:
        # Create model and optimizer
        model, optimizer = create_model_and_optimizer(num_classes=10, lr=0.03)
        print("✓ Model and optimizer created")
        
        # Create FixMatch trainer
        device = torch.device('cpu')  # Use CPU for testing
        fixmatch = FixMatch(
            model=model,
            optimizer=optimizer,
            threshold=0.95,
            lambda_u=1.0,
            use_amp=False,  # Disable AMP for CPU testing
            device=device
        )
        print("✓ FixMatch trainer created")
        
        # Create dummy batches
        batch_size = 4
        labeled_images = torch.randn(batch_size, 3, 32, 32)
        labeled_targets = torch.randint(0, 10, (batch_size,))
        labeled_batch = (labeled_images, labeled_targets)
        
        unlabeled_weak = torch.randn(batch_size * 2, 3, 32, 32)
        unlabeled_strong = torch.randn(batch_size * 2, 3, 32, 32)
        unlabeled_batch = ((unlabeled_weak, unlabeled_strong), torch.zeros(batch_size * 2))
        
        print("✓ Dummy batches created")
        
        # Test training step
        metrics = fixmatch.train_step(labeled_batch, unlabeled_batch)
        print("✓ Training step successful")
        print(f"  Loss: {metrics['loss']:.4f}")
        print(f"  Labeled loss: {metrics['loss_labeled']:.4f}")
        print(f"  Unlabeled loss: {metrics['loss_unlabeled']:.4f}")
        print(f"  Labeled accuracy: {metrics['labeled_acc']:.4f}")
        print(f"  Mask probability: {metrics['mask_prob']:.4f}")
        
        # Test evaluation
        test_images = torch.randn(8, 3, 32, 32)
        test_targets = torch.randint(0, 10, (8,))
        test_dataset = torch.utils.data.TensorDataset(test_images, test_targets)
        test_loader = DataLoader(test_dataset, batch_size=4)
        
        eval_metrics = fixmatch.evaluate(test_loader)
        print("✓ Evaluation successful")
        print(f"  Test loss: {eval_metrics['test_loss']:.4f}")
        print(f"  Test accuracy: {eval_metrics['test_acc']:.4f}")
        
    except Exception as e:
        print(f"✗ FixMatch error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def main():
    """Run all tests"""
    print("FixMatch Implementation Test Suite")
    print("="*60)
    
    tests = [
        ("Model", test_model),
        ("Augmentation", test_augmentation),
        ("Data Loading", test_data_loading),
        ("FixMatch Algorithm", test_fixmatch)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result is None:
                result = True  # Assume success if no return value
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tests passed! Implementation is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
