"""
CIFAR-10 Semi-supervised Learning Dataset
Implements data loading for FixMatch with labeled and unlabeled splits
"""
import torch
import torchvision
import numpy as np
from torch.utils.data import Dataset, DataLoader, Subset
import random
import itertools
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from augmentation import get_cifar10_transforms


class CIFAR10SSL(Dataset):
    """
    CIFAR-10 dataset for semi-supervised learning
    """
    def __init__(self, root='./data', train=True, transform=None, target_transform=None,
                 download=True, indexs=None):
        self.cifar10 = torchvision.datasets.CIFAR10(root=root, train=train,
                                                     transform=transform,
                                                     target_transform=target_transform,
                                                     download=download)
        if indexs is not None:
            self.data = self.cifar10.data[indexs]
            self.targets = np.array(self.cifar10.targets)[indexs]
        else:
            self.data = self.cifar10.data
            self.targets = np.array(self.cifar10.targets)
        
        self.transform = transform
        self.target_transform = target_transform

    def __getitem__(self, index):
        img, target = self.data[index], self.targets[index]
        img = torchvision.transforms.ToPILImage()(img)
        
        if self.transform is not None:
            img = self.transform(img)
        
        if self.target_transform is not None:
            target = self.target_transform(target)
        
        return img, target

    def __len__(self):
        return len(self.data)


def get_cifar10_ssl_datasets(root='./data', n_labeled=4000, n_classes=10):
    """
    Get CIFAR-10 datasets for semi-supervised learning
    
    Args:
        root: data directory
        n_labeled: number of labeled samples per class
        n_classes: number of classes (10 for CIFAR-10)
    
    Returns:
        labeled_dataset, unlabeled_dataset, test_dataset
    """
    transform_labeled, transform_unlabeled, transform_test = get_cifar10_transforms()
    
    # Load full training dataset to get indices
    base_dataset = torchvision.datasets.CIFAR10(root=root, train=True, download=True)
    
    # Get labeled and unlabeled indices
    labeled_idx, unlabeled_idx = split_ssl_data(base_dataset, n_labeled, n_classes)
    
    # Create datasets
    labeled_dataset = CIFAR10SSL(root=root, train=True, transform=transform_labeled,
                                indexs=labeled_idx)
    
    unlabeled_dataset = CIFAR10SSL(root=root, train=True, transform=transform_unlabeled,
                                  indexs=unlabeled_idx)
    
    test_dataset = CIFAR10SSL(root=root, train=False, transform=transform_test)
    
    return labeled_dataset, unlabeled_dataset, test_dataset


def split_ssl_data(base_dataset, n_labeled, n_classes, seed=0):
    """
    Split dataset into labeled and unlabeled parts
    
    Args:
        base_dataset: full training dataset
        n_labeled: number of labeled samples per class
        n_classes: number of classes
        seed: random seed
    
    Returns:
        labeled_idx, unlabeled_idx
    """
    np.random.seed(seed)
    random.seed(seed)
    
    labels = np.array(base_dataset.targets)
    labeled_idx = []
    unlabeled_idx = []
    
    for i in range(n_classes):
        idx = np.where(labels == i)[0]
        np.random.shuffle(idx)
        labeled_idx.extend(idx[:n_labeled])
        unlabeled_idx.extend(idx[n_labeled:])
    
    np.random.shuffle(labeled_idx)
    np.random.shuffle(unlabeled_idx)
    
    return labeled_idx, unlabeled_idx


def get_cifar10_dataloaders(root='./data', n_labeled=4000, batch_size=64, 
                           mu=7, num_workers=4):
    """
    Get CIFAR-10 data loaders for FixMatch
    
    Args:
        root: data directory
        n_labeled: number of labeled samples per class
        batch_size: batch size for labeled data
        mu: ratio of unlabeled to labeled batch size
        num_workers: number of workers for data loading
    
    Returns:
        labeled_loader, unlabeled_loader, test_loader
    """
    labeled_dataset, unlabeled_dataset, test_dataset = get_cifar10_ssl_datasets(
        root=root, n_labeled=n_labeled)
    
    # Calculate total labeled samples
    total_labeled = n_labeled * 10  # 10 classes in CIFAR-10
    
    # Adjust batch size if necessary
    if batch_size > total_labeled:
        batch_size = total_labeled
    
    labeled_loader = DataLoader(
        labeled_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        drop_last=True
    )
    
    unlabeled_loader = DataLoader(
        unlabeled_dataset,
        batch_size=batch_size * mu,
        shuffle=True,
        num_workers=num_workers,
        drop_last=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers
    )
    
    return labeled_loader, unlabeled_loader, test_loader


class TwoStreamBatchSampler:
    """
    Batch sampler for FixMatch that ensures balanced sampling from labeled and unlabeled data
    """
    def __init__(self, primary_indices, secondary_indices, batch_size, secondary_batch_size):
        self.primary_indices = primary_indices
        self.secondary_indices = secondary_indices
        self.secondary_batch_size = secondary_batch_size
        self.primary_batch_size = batch_size - secondary_batch_size

        assert len(self.primary_indices) >= self.primary_batch_size > 0
        assert len(self.secondary_indices) >= self.secondary_batch_size > 0

    def __iter__(self):
        primary_iter = iterate_once(self.primary_indices)
        secondary_iter = iterate_eternally(self.secondary_indices)
        return (
            primary_batch + secondary_batch
            for (primary_batch, secondary_batch)
            in zip(grouper(primary_iter, self.primary_batch_size),
                   grouper(secondary_iter, self.secondary_batch_size))
        )

    def __len__(self):
        return len(self.primary_indices) // self.primary_batch_size


def iterate_once(iterable):
    return np.random.permutation(iterable)


def iterate_eternally(indices):
    def infinite_shuffles():
        while True:
            yield np.random.permutation(indices)
    return itertools.chain.from_iterable(infinite_shuffles())


def grouper(iterable, n):
    "Collect data into fixed-length chunks or blocks"
    args = [iter(iterable)] * n
    return zip(*args)
