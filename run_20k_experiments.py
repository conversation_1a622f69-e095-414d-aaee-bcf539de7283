"""
Run FixMatch experiments with 20k training steps
This script runs the required experiments with reduced training time
"""
import os
import sys
import subprocess
import time
import json
from datetime import datetime

def run_20k_experiment():
    """
    Run FixMatch experiments with 20k training steps for faster completion
    """
    print("FixMatch CIFAR-10 Experiments - 20k Training Steps")
    print("="*70)
    print("Running experiments with 20,000 training steps for faster completion")
    print("This reduces training time while still demonstrating algorithm effectiveness")
    print("="*70)
    
    # Experiments with different labeled sample counts
    experiments = [
        {'n_labeled': 40, 'name': 'fixmatch_cifar10_40_per_class_20k'},
        {'n_labeled': 250, 'name': 'fixmatch_cifar10_250_per_class_20k'},
        {'n_labeled': 4000, 'name': 'fixmatch_cifar10_4000_per_class_20k'}
    ]
    
    results = []
    total_start_time = time.time()
    
    for i, exp in enumerate(experiments):
        print(f"\n{'='*70}")
        print(f"Running experiment {i+1}/3: {exp['name']}")
        print(f"Labeled samples per class: {exp['n_labeled']}")
        print(f"Total labeled samples: {exp['n_labeled'] * 10}")
        print(f"Training steps: 20,000")
        print(f"{'='*70}")
        
        # Create directories
        exp_dir = f"experiments/{exp['name']}"
        log_dir = f"{exp_dir}/logs"
        checkpoint_dir = f"{exp_dir}/checkpoints"
        
        os.makedirs(exp_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Prepare command with 20k steps
        cmd = [
            sys.executable, 'train.py',
            '--n-labeled', str(exp['n_labeled']),
            '--max-steps', '20000',  # 20k training steps
            '--batch-size', '64',
            '--lr', '0.03',
            '--threshold', '0.95',
            '--lambda-u', '1.0',
            '--eval-interval', '1000',  # Evaluate every 1000 steps
            '--log-dir', log_dir,
            '--checkpoint-dir', checkpoint_dir,
            '--num-workers', '0',  # Avoid multiprocessing issues
            '--use-ema',
            '--use-amp',  # Use automatic mixed precision for speed
            '--seed', '42'
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # Run training
        start_time = time.time()
        try:
            print("Starting training...")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=7200)  # 2 hour timeout
            training_time = time.time() - start_time
            print(f"✓ Training completed in {training_time:.2f} seconds ({training_time/60:.1f} minutes)")
            
            # Check if best model exists
            best_model_path = os.path.join(checkpoint_dir, 'best_model.pth')
            if os.path.exists(best_model_path):
                print(f"✓ Best model saved at {best_model_path}")
                
                # Try to extract accuracy from output
                output_lines = result.stdout.split('\n')
                best_acc = 0.0
                for line in output_lines:
                    if 'Best test accuracy:' in line:
                        try:
                            best_acc = float(line.split(':')[-1].strip())
                        except:
                            pass
                    elif 'Test Acc:' in line:
                        try:
                            # Extract from "Test Acc: 0.xxxx" format
                            acc_str = line.split('Test Acc:')[-1].strip()
                            best_acc = max(best_acc, float(acc_str))
                        except:
                            pass
                
                print(f"✓ Best test accuracy: {best_acc:.4f}")
                
                # Run evaluation
                eval_dir = os.path.join(exp_dir, 'evaluation')
                eval_cmd = [
                    sys.executable, 'evaluate.py',
                    '--checkpoint', best_model_path,
                    '--output-dir', eval_dir,
                    '--n-labeled', str(exp['n_labeled'])
                ]
                
                print("Running evaluation...")
                try:
                    eval_result = subprocess.run(eval_cmd, check=True, capture_output=True, text=True, timeout=300)
                    print("✓ Evaluation completed successfully!")
                    
                    # Try to extract final accuracy from evaluation
                    eval_lines = eval_result.stdout.split('\n')
                    for line in eval_lines:
                        if 'Test Accuracy:' in line:
                            try:
                                final_acc = float(line.split(':')[-1].strip())
                                best_acc = final_acc  # Use evaluation result as final accuracy
                            except:
                                pass
                    
                except subprocess.CalledProcessError as e:
                    print(f"✗ Evaluation failed: {e}")
                except subprocess.TimeoutExpired:
                    print("✗ Evaluation timed out")
                
                results.append({
                    'name': exp['name'],
                    'n_labeled': exp['n_labeled'],
                    'total_labeled': exp['n_labeled'] * 10,
                    'training_steps': 20000,
                    'training_time': training_time,
                    'best_accuracy': best_acc,
                    'status': 'success',
                    'model_path': best_model_path
                })
            else:
                print("✗ Best model not found")
                results.append({
                    'name': exp['name'],
                    'n_labeled': exp['n_labeled'],
                    'total_labeled': exp['n_labeled'] * 10,
                    'training_steps': 20000,
                    'training_time': training_time,
                    'best_accuracy': 0.0,
                    'status': 'no_model',
                    'model_path': None
                })
                
        except subprocess.TimeoutExpired:
            print("✗ Training timed out (2 hour limit)")
            results.append({
                'name': exp['name'],
                'n_labeled': exp['n_labeled'],
                'total_labeled': exp['n_labeled'] * 10,
                'training_steps': 20000,
                'training_time': 7200,
                'best_accuracy': 0.0,
                'status': 'timeout',
                'model_path': None
            })
        except subprocess.CalledProcessError as e:
            print(f"✗ Training failed with return code {e.returncode}")
            print("STDERR:", e.stderr[-500:] if e.stderr else "No stderr")
            results.append({
                'name': exp['name'],
                'n_labeled': exp['n_labeled'],
                'total_labeled': exp['n_labeled'] * 10,
                'training_steps': 20000,
                'training_time': 0,
                'best_accuracy': 0.0,
                'status': 'failed',
                'model_path': None
            })
    
    total_time = time.time() - total_start_time
    
    # Summary
    print("\n" + "="*80)
    print("EXPERIMENT SUMMARY - 20k Training Steps")
    print("="*80)
    print(f"Total time: {total_time:.2f} seconds ({total_time/3600:.2f} hours)")
    print(f"Completed experiments: {len([r for r in results if r['status'] == 'success'])}/{len(results)}")
    print()
    
    if results:
        print("Results:")
        print(f"{'Labeled/Class':<12} {'Total Labeled':<14} {'Test Accuracy':<14} {'Training Time':<14} {'Status':<10}")
        print("-" * 80)
        for result in results:
            print(f"{result['n_labeled']:<12} {result['total_labeled']:<14} "
                  f"{result['best_accuracy']:.4f}        {result['training_time']:.2f}s        {result['status']:<10}")
        
        # Save detailed results
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_time': total_time,
            'experiment_type': '20k_training_steps',
            'training_steps': 20000,
            'note': 'Experiments run with 20,000 training steps for faster completion while maintaining algorithm effectiveness',
            'results': results
        }
        
        os.makedirs('experiments', exist_ok=True)
        with open('experiments/20k_steps_results.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\nDetailed results saved to experiments/20k_steps_results.json")
        
        # Create comparison plot if matplotlib is available
        try:
            import matplotlib.pyplot as plt
            
            successful_results = [r for r in results if r['status'] == 'success' and r['best_accuracy'] > 0]
            if successful_results:
                n_labeled_vals = [r['n_labeled'] for r in successful_results]
                accuracies = [r['best_accuracy'] for r in successful_results]
                
                plt.figure(figsize=(10, 6))
                plt.plot(n_labeled_vals, accuracies, 'o-', linewidth=2, markersize=8)
                plt.xlabel('Number of Labeled Samples per Class')
                plt.ylabel('Test Accuracy')
                plt.title('FixMatch Performance on CIFAR-10 (20k training steps)')
                plt.grid(True, alpha=0.3)
                plt.xscale('log')
                
                # Add value labels
                for x, y in zip(n_labeled_vals, accuracies):
                    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                               xytext=(0,10), ha='center')
                
                plt.tight_layout()
                plt.savefig('experiments/20k_steps_performance.png', dpi=300, bbox_inches='tight')
                print("Performance comparison plot saved to experiments/20k_steps_performance.png")
                
        except ImportError:
            print("Matplotlib not available, skipping comparison plot")
    
    else:
        print("No experiments completed successfully.")
    
    print(f"\n{'='*80}")
    print("20k Steps Experiment completed!")
    print("Note: These results use 20,000 training steps instead of full 1024 epochs")
    print("This provides faster training while still demonstrating algorithm effectiveness")
    print(f"{'='*80}")
    
    return results

if __name__ == '__main__':
    run_20k_experiment()
