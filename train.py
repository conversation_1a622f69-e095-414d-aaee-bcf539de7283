"""
Training script for FixMatch on CIFAR-10
"""
import os
import time
import logging
import argparse
import numpy as np
import torch
import torch.nn as nn
# from torch.utils.tensorboard import SummaryWriter  # Commented out due to missing dependency
# from tqdm import tqdm  # Commented out due to installation issues

from data.cifar10_ssl import get_cifar10_dataloaders
from fixmatch import FixMatch, create_model_and_optimizer, get_cosine_schedule_with_warmup, EMA


def setup_logging(log_dir):
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'train.log')),
            logging.StreamHandler()
        ]
    )


def set_seed(seed):
    """Set random seed for reproducibility"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def save_checkpoint(state, checkpoint_dir, filename='checkpoint.pth'):
    """Save model checkpoint"""
    os.makedirs(checkpoint_dir, exist_ok=True)
    filepath = os.path.join(checkpoint_dir, filename)
    torch.save(state, filepath)
    logging.info(f'Checkpoint saved to {filepath}')


def train_fixmatch(args):
    """
    Main training function for FixMatch
    """
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f'Using device: {device}')
    
    # Set random seed
    set_seed(args.seed)
    
    # Create data loaders
    logging.info(f'Creating data loaders with {args.n_labeled} labeled samples per class')
    labeled_loader, unlabeled_loader, test_loader = get_cifar10_dataloaders(
        root=args.data_root,
        n_labeled=args.n_labeled,
        batch_size=args.batch_size,
        mu=args.mu,
        num_workers=args.num_workers
    )
    
    logging.info(f'Labeled samples: {len(labeled_loader.dataset)}')
    logging.info(f'Unlabeled samples: {len(unlabeled_loader.dataset)}')
    logging.info(f'Test samples: {len(test_loader.dataset)}')
    
    # Create model and optimizer
    model, optimizer = create_model_and_optimizer(
        num_classes=10,
        lr=args.lr,
        weight_decay=args.weight_decay,
        momentum=args.momentum
    )
    model = model.to(device)
    
    # Calculate total training steps
    if args.max_steps is not None:
        total_steps = args.max_steps
        # Adjust epochs based on max_steps
        steps_per_epoch = len(labeled_loader)
        args.epochs = (total_steps + steps_per_epoch - 1) // steps_per_epoch  # Ceiling division
        logging.info(f'Using max_steps={total_steps}, adjusted epochs to {args.epochs}')
    else:
        total_steps = args.epochs * len(labeled_loader)

    warmup_steps = int(0.1 * total_steps)  # 10% warmup
    
    # Create scheduler
    scheduler = get_cosine_schedule_with_warmup(
        optimizer, warmup_steps, total_steps
    )
    
    # Create FixMatch trainer
    fixmatch = FixMatch(
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        threshold=args.threshold,
        lambda_u=args.lambda_u,
        use_amp=args.use_amp,
        device=device
    )
    
    # EMA
    if args.use_ema:
        ema = EMA(model, decay=args.ema_decay)
    
    # Tensorboard writer (disabled due to missing dependency)
    writer = None  # SummaryWriter(log_dir=os.path.join(args.log_dir, 'tensorboard'))
    
    # Training loop
    logging.info('Starting training...')
    best_acc = 0.0
    global_step = 0

    for epoch in range(args.epochs):
        if args.max_steps is not None and global_step >= args.max_steps:
            logging.info(f'Reached max_steps={args.max_steps}, stopping training')
            break
        epoch_start_time = time.time()
        
        # Training
        model.train()
        epoch_losses = []
        epoch_labeled_losses = []
        epoch_unlabeled_losses = []
        epoch_labeled_accs = []
        epoch_mask_probs = []
        
        # Create iterators
        labeled_iter = iter(labeled_loader)
        unlabeled_iter = iter(unlabeled_loader)
        
        # progress_bar = tqdm(range(len(labeled_loader)), desc=f'Epoch {epoch+1}/{args.epochs}')

        for batch_idx in range(len(labeled_loader)):
            try:
                labeled_batch = next(labeled_iter)
            except StopIteration:
                labeled_iter = iter(labeled_loader)
                labeled_batch = next(labeled_iter)
            
            try:
                unlabeled_batch = next(unlabeled_iter)
            except StopIteration:
                unlabeled_iter = iter(unlabeled_loader)
                unlabeled_batch = next(unlabeled_iter)
            
            # Training step
            metrics = fixmatch.train_step(labeled_batch, unlabeled_batch)
            
            # Update EMA
            if args.use_ema:
                ema.update()
            
            # Log metrics
            epoch_losses.append(metrics['loss'])
            epoch_labeled_losses.append(metrics['loss_labeled'])
            epoch_unlabeled_losses.append(metrics['loss_unlabeled'])
            epoch_labeled_accs.append(metrics['labeled_acc'])
            epoch_mask_probs.append(metrics['mask_prob'])
            
            # Update progress (print every 10 batches)
            if batch_idx % 10 == 0:
                print(f"Epoch {epoch+1}/{args.epochs}, Batch {batch_idx}/{len(labeled_loader)}: "
                      f"Loss={metrics['loss']:.4f}, L_sup={metrics['loss_labeled']:.4f}, "
                      f"L_unsup={metrics['loss_unlabeled']:.4f}, Acc={metrics['labeled_acc']:.4f}, "
                      f"Mask={metrics['mask_prob']:.4f}")
            
            # Log to tensorboard (disabled)
            if global_step % args.log_interval == 0:
                if writer is not None:
                    writer.add_scalar('Train/Loss', metrics['loss'], global_step)
                    writer.add_scalar('Train/Loss_Labeled', metrics['loss_labeled'], global_step)
                    writer.add_scalar('Train/Loss_Unlabeled', metrics['loss_unlabeled'], global_step)
                    writer.add_scalar('Train/Labeled_Acc', metrics['labeled_acc'], global_step)
                    writer.add_scalar('Train/Mask_Prob', metrics['mask_prob'], global_step)
                    writer.add_scalar('Train/LR', optimizer.param_groups[0]['lr'], global_step)
            
            global_step += 1

            # Check if we've reached max steps
            if args.max_steps is not None and global_step >= args.max_steps:
                logging.info(f'Reached max_steps={args.max_steps} at epoch {epoch+1}, batch {batch_idx}')
                break
        
        # Epoch statistics
        epoch_time = time.time() - epoch_start_time
        avg_loss = np.mean(epoch_losses)
        avg_labeled_loss = np.mean(epoch_labeled_losses)
        avg_unlabeled_loss = np.mean(epoch_unlabeled_losses)
        avg_labeled_acc = np.mean(epoch_labeled_accs)
        avg_mask_prob = np.mean(epoch_mask_probs)
        
        logging.info(f'Epoch {epoch+1}/{args.epochs} - Time: {epoch_time:.2f}s')
        logging.info(f'Loss: {avg_loss:.4f}, L_sup: {avg_labeled_loss:.4f}, L_unsup: {avg_unlabeled_loss:.4f}')
        logging.info(f'Labeled Acc: {avg_labeled_acc:.4f}, Mask Prob: {avg_mask_prob:.4f}')
        
        # Evaluation
        if (epoch + 1) % args.eval_interval == 0:
            # Use EMA model for evaluation if available
            if args.use_ema:
                ema.apply_shadow()
            
            eval_metrics = fixmatch.evaluate(test_loader)
            test_acc = eval_metrics['test_acc']
            test_loss = eval_metrics['test_loss']
            
            logging.info(f'Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.4f}')
            
            # Log to tensorboard (disabled)
            if writer is not None:
                writer.add_scalar('Test/Loss', test_loss, epoch)
                writer.add_scalar('Test/Accuracy', test_acc, epoch)
            
            # Save best model
            if test_acc > best_acc:
                best_acc = test_acc
                save_checkpoint({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'best_acc': best_acc,
                    'args': args
                }, args.checkpoint_dir, 'best_model.pth')
            
            # Restore original model if using EMA
            if args.use_ema:
                ema.restore()
        
        # Save checkpoint
        if (epoch + 1) % args.save_interval == 0:
            save_checkpoint({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_acc': best_acc,
                'args': args
            }, args.checkpoint_dir, f'checkpoint_epoch_{epoch+1}.pth')
    
    logging.info(f'Training completed! Best test accuracy: {best_acc:.4f}')
    if writer is not None:
        writer.close()
    
    return best_acc


def main():
    parser = argparse.ArgumentParser(description='FixMatch Training on CIFAR-10')
    
    # Data arguments
    parser.add_argument('--data-root', default='./data', type=str, help='Data directory')
    parser.add_argument('--n-labeled', default=4000, type=int, help='Number of labeled samples per class')
    
    # Model arguments
    parser.add_argument('--batch-size', default=64, type=int, help='Batch size for labeled data')
    parser.add_argument('--mu', default=7, type=int, help='Ratio of unlabeled to labeled batch size')
    
    # Training arguments
    parser.add_argument('--epochs', default=1024, type=int, help='Number of training epochs')
    parser.add_argument('--max-steps', default=None, type=int, help='Maximum training steps (overrides epochs if set)')
    parser.add_argument('--lr', default=0.03, type=float, help='Learning rate')
    parser.add_argument('--weight-decay', default=5e-4, type=float, help='Weight decay')
    parser.add_argument('--momentum', default=0.9, type=float, help='SGD momentum')
    
    # FixMatch arguments
    parser.add_argument('--threshold', default=0.95, type=float, help='Confidence threshold for pseudo-labels')
    parser.add_argument('--lambda-u', default=1.0, type=float, help='Weight for unsupervised loss')
    
    # EMA arguments
    parser.add_argument('--use-ema', action='store_true', help='Use exponential moving average')
    parser.add_argument('--ema-decay', default=0.999, type=float, help='EMA decay rate')
    
    # System arguments
    parser.add_argument('--use-amp', action='store_true', help='Use automatic mixed precision')
    parser.add_argument('--num-workers', default=4, type=int, help='Number of data loading workers')
    parser.add_argument('--seed', default=42, type=int, help='Random seed')
    
    # Logging arguments
    parser.add_argument('--log-dir', default='./logs', type=str, help='Log directory')
    parser.add_argument('--checkpoint-dir', default='./checkpoints', type=str, help='Checkpoint directory')
    parser.add_argument('--log-interval', default=100, type=int, help='Log interval')
    parser.add_argument('--eval-interval', default=10, type=int, help='Evaluation interval')
    parser.add_argument('--save-interval', default=100, type=int, help='Save interval')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_dir)
    
    # Log arguments
    logging.info('Arguments:')
    for arg, value in vars(args).items():
        logging.info(f'  {arg}: {value}')
    
    # Train
    best_acc = train_fixmatch(args)
    
    return best_acc


if __name__ == '__main__':
    main()
