# FixMatch Implementation for CIFAR-10

This repository contains a PyTorch implementation of FixMatch for semi-supervised learning on CIFAR-10, as required for the Pattern Recognition Course Lab 3.

## Overview

FixMatch is a semi-supervised learning algorithm that combines consistency regularization and pseudo-labeling. It uses:
- **Weak augmentation** to generate pseudo-labels for unlabeled data
- **Strong augmentation** with the pseudo-labels for consistency training
- **Confidence thresholding** to select high-quality pseudo-labels

## Key Features

- **WideResNet-28-2** backbone network as specified in the original paper
- **RandAugment** for strong data augmentation
- **Exponential Moving Average (EMA)** for model parameters
- **Automatic Mixed Precision (AMP)** support for faster training
- **Comprehensive evaluation** with visualization tools

## Project Structure

```
├── models/
│   ├── __init__.py
│   └── wideresnet.py          # WideResNet-28-2 implementation
├── data/
│   ├── __init__.py
│   └── cifar10_ssl.py         # CIFAR-10 semi-supervised data loading
├── augmentation.py            # Data augmentation (weak/strong)
├── fixmatch.py               # FixMatch algorithm implementation
├── train.py                  # Training script
├── evaluate.py               # Evaluation script
├── main.py                   # Main experiment runner
├── test_implementation.py    # Test suite
├── requirements.txt          # Dependencies
└── README.md                # This file
```

## Installation

1. Clone or download this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Quick Test

First, test the implementation:
```bash
python test_implementation.py
```

### Single Experiment

Train FixMatch with 4000 labeled samples per class:
```bash
python train.py --n-labeled 4000 --epochs 1024 --use-ema --use-amp
```

### Full Experiments (40, 250, 4000 samples)

Run all experiments as required:
```bash
python main.py --experiments 40 250 4000 --epochs 1024 --use-ema --use-amp
```

### Evaluation

Evaluate a trained model:
```bash
python evaluate.py --checkpoint ./experiments/fixmatch_cifar10_4000_per_class/checkpoints/best_model.pth
```

## Experiment Configuration

### Default Parameters (following FixMatch paper)
- **Model**: WideResNet-28-2
- **Batch size**: 64 (labeled), 448 (unlabeled, μ=7)
- **Learning rate**: 0.03 with cosine annealing
- **Confidence threshold**: 0.95
- **Unsupervised loss weight (λ_u)**: 1.0
- **Training epochs**: 1024
- **EMA decay**: 0.999

### Command Line Arguments

Key arguments for `train.py`:
- `--n-labeled`: Number of labeled samples per class (40, 250, 4000)
- `--epochs`: Number of training epochs (default: 1024)
- `--batch-size`: Batch size for labeled data (default: 64)
- `--threshold`: Confidence threshold for pseudo-labels (default: 0.95)
- `--lambda-u`: Weight for unsupervised loss (default: 1.0)
- `--use-ema`: Enable exponential moving average
- `--use-amp`: Enable automatic mixed precision

## Expected Results

Based on the FixMatch paper, expected test accuracies on CIFAR-10:
- **40 labels per class (400 total)**: ~88-92%
- **250 labels per class (2500 total)**: ~94-95%
- **4000 labels per class (40000 total)**: ~95-96%

## Implementation Details

### Core Algorithm (FixMatch)

1. **Supervised Loss**: Standard cross-entropy on labeled data
2. **Pseudo-labeling**: Generate pseudo-labels using weakly augmented unlabeled data
3. **Consistency Loss**: Train on strongly augmented versions using pseudo-labels
4. **Confidence Thresholding**: Only use pseudo-labels with confidence > threshold

### Data Augmentation

- **Weak**: RandomHorizontalFlip + RandomCrop with padding
- **Strong**: Weak + RandAugment + Cutout

### Network Architecture

WideResNet-28-2:
- Depth: 28 layers
- Width factor: 2
- Dropout: 0.0 (as in original paper)

## Files Description

### Core Implementation
- `fixmatch.py`: Main FixMatch algorithm with training and evaluation logic
- `models/wideresnet.py`: WideResNet architecture implementation
- `augmentation.py`: Data augmentation strategies (weak/strong)
- `data/cifar10_ssl.py`: Semi-supervised data loading for CIFAR-10

### Experiment Scripts
- `train.py`: Single experiment training script
- `main.py`: Run multiple experiments with different labeled data amounts
- `evaluate.py`: Comprehensive model evaluation with visualizations
- `test_implementation.py`: Test suite to verify implementation

## Monitoring and Visualization

The implementation includes:
- **TensorBoard logging** for training metrics
- **Confusion matrix** visualization
- **Per-class accuracy** analysis
- **Confidence calibration** plots
- **Training progress** tracking

## Comparison with TorchSSL/USB

The implementation can be compared with existing libraries:
- Install USB: `pip install usb-ssl`
- Compare results with the reference implementation
- Analyze differences in performance and implementation details

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch size or disable AMP
2. **Slow training**: Enable AMP and use multiple workers
3. **Poor performance**: Check data augmentation and hyperparameters

### Performance Tips

- Use `--use-amp` for faster training with mixed precision
- Set `--num-workers > 0` for faster data loading
- Monitor GPU utilization and adjust batch size accordingly

## References

1. FixMatch: Simplifying Semi-Supervised Learning with Consistency and Confidence
2. Wide Residual Networks
3. RandAugment: Practical automated data augmentation
4. TorchSSL: A PyTorch-based Toolbox for Semi-Supervised Learning

## License

This implementation is for educational purposes as part of the Pattern Recognition course.
