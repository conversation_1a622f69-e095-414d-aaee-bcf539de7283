"""
Quick experiment script to test FixMatch with reduced epochs
"""
import os
import sys
import subprocess
import time

def run_quick_experiment():
    """
    Run a quick experiment with reduced epochs for testing
    """
    print("Quick FixMatch Experiment")
    print("="*50)
    
    # Test with 40 labeled samples per class, only 5 epochs
    experiments = [
        {'n_labeled': 40, 'epochs': 5, 'name': 'quick_test_40'},
        {'n_labeled': 250, 'epochs': 5, 'name': 'quick_test_250'}
    ]
    
    results = []
    
    for exp in experiments:
        print(f"\nRunning experiment: {exp['name']}")
        print(f"Labeled samples per class: {exp['n_labeled']}")
        print(f"Epochs: {exp['epochs']}")
        
        # Create directories
        exp_dir = f"experiments/{exp['name']}"
        log_dir = f"{exp_dir}/logs"
        checkpoint_dir = f"{exp_dir}/checkpoints"
        
        os.makedirs(exp_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Prepare command
        cmd = [
            sys.executable, 'train.py',
            '--n-labeled', str(exp['n_labeled']),
            '--epochs', str(exp['epochs']),
            '--batch-size', '16',  # Smaller batch size for quick testing
            '--eval-interval', '2',  # Evaluate every 2 epochs
            '--log-dir', log_dir,
            '--checkpoint-dir', checkpoint_dir,
            '--num-workers', '0',  # Avoid multiprocessing issues
            '--use-ema'
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # Run training
        start_time = time.time()
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=600)  # 10 min timeout
            training_time = time.time() - start_time
            print(f"✓ Training completed in {training_time:.2f} seconds")
            
            # Check if best model exists
            best_model_path = os.path.join(checkpoint_dir, 'best_model.pth')
            if os.path.exists(best_model_path):
                print(f"✓ Best model saved at {best_model_path}")
                
                # Try to extract accuracy from output
                output_lines = result.stdout.split('\n')
                best_acc = 0.0
                for line in output_lines:
                    if 'Best test accuracy:' in line:
                        try:
                            best_acc = float(line.split(':')[-1].strip())
                        except:
                            pass
                
                results.append({
                    'name': exp['name'],
                    'n_labeled': exp['n_labeled'],
                    'epochs': exp['epochs'],
                    'training_time': training_time,
                    'best_accuracy': best_acc,
                    'status': 'success'
                })
            else:
                print("✗ Best model not found")
                results.append({
                    'name': exp['name'],
                    'n_labeled': exp['n_labeled'],
                    'epochs': exp['epochs'],
                    'training_time': training_time,
                    'best_accuracy': 0.0,
                    'status': 'no_model'
                })
                
        except subprocess.TimeoutExpired:
            print("✗ Training timed out")
            results.append({
                'name': exp['name'],
                'n_labeled': exp['n_labeled'],
                'epochs': exp['epochs'],
                'training_time': 600,
                'best_accuracy': 0.0,
                'status': 'timeout'
            })
        except subprocess.CalledProcessError as e:
            print(f"✗ Training failed with return code {e.returncode}")
            print("STDERR:", e.stderr[-500:] if e.stderr else "No stderr")
            results.append({
                'name': exp['name'],
                'n_labeled': exp['n_labeled'],
                'epochs': exp['epochs'],
                'training_time': 0,
                'best_accuracy': 0.0,
                'status': 'failed'
            })
    
    # Summary
    print("\n" + "="*60)
    print("EXPERIMENT SUMMARY")
    print("="*60)
    
    for result in results:
        print(f"Experiment: {result['name']}")
        print(f"  Labeled samples/class: {result['n_labeled']}")
        print(f"  Epochs: {result['epochs']}")
        print(f"  Training time: {result['training_time']:.2f}s")
        print(f"  Best accuracy: {result['best_accuracy']:.4f}")
        print(f"  Status: {result['status']}")
        print()
    
    # Save results
    import json
    os.makedirs('experiments', exist_ok=True)
    with open('experiments/quick_experiment_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("Results saved to experiments/quick_experiment_results.json")
    
    return results

if __name__ == '__main__':
    run_quick_experiment()
